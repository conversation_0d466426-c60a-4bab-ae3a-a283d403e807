<?php

namespace Database\Seeders;

use App\Models\Donation;
use App\Models\User;
use App\Models\Group;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DonationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user and group for testing
        $user = User::first();
        $group = Group::first();

        if (!$user || !$group) {
            $this->command->info('No users or groups found. Please create some first.');
            return;
        }

        // Create sample donations
        $donations = [
            [
                'user_id' => $user->id,
                'group_id' => $group->id,
                'amount' => 25.00,
                'currency' => 'USD',
                'type' => 'one-time',
                'status' => 'completed',
                'payment_method' => 'stripe',
                'transaction_id' => 'ch_1234567890',
                'country' => 'US',
                'processed_at' => now()->subDays(30),
                'created_at' => now()->subDays(30),
            ],
            [
                'user_id' => $user->id,
                'group_id' => null, // Common Change donation
                'amount' => 50.00,
                'currency' => 'USD',
                'type' => 'recurring',
                'frequency' => 'monthly',
                'status' => 'completed',
                'payment_method' => 'stripe',
                'transaction_id' => 'ch_0987654321',
                'country' => 'US',
                'processed_at' => now()->subDays(15),
                'created_at' => now()->subDays(15),
            ],
            [
                'user_id' => $user->id,
                'group_id' => $group->id,
                'amount' => 100.00,
                'currency' => 'USD',
                'type' => 'one-time',
                'status' => 'pending',
                'payment_method' => 'stripe',
                'country' => 'US',
                'created_at' => now()->subDays(5),
            ],
            [
                'user_id' => $user->id,
                'group_id' => null,
                'amount' => 75.00,
                'currency' => 'GBP',
                'type' => 'one-time',
                'status' => 'completed',
                'payment_method' => 'donorbox',
                'transaction_id' => 'db_1122334455',
                'country' => 'UK',
                'processed_at' => now()->subDays(7),
                'created_at' => now()->subDays(7),
            ],
            [
                'user_id' => $user->id,
                'group_id' => $group->id,
                'amount' => 20.00,
                'currency' => 'USD',
                'type' => 'recurring',
                'frequency' => 'weekly',
                'status' => 'failed',
                'payment_method' => 'stripe',
                'country' => 'US',
                'notes' => 'Card declined',
                'created_at' => now()->subDays(2),
            ],
        ];

        foreach ($donations as $donationData) {
            Donation::create($donationData);
        }

        $this->command->info('Sample donations created successfully!');
    }
}
