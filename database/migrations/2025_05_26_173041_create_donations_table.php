<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('donations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('group_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD'); // USD, GBP, ZAR
            $table->enum('type', ['one-time', 'recurring'])->default('one-time');
            $table->enum('frequency', ['weekly', 'monthly'])->nullable(); // for recurring donations
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->enum('payment_method', ['stripe', 'paypal', 'donorbox', 'bank_transfer'])->default('stripe');
            $table->string('transaction_id')->nullable(); // External payment processor transaction ID
            $table->string('customer_id')->nullable(); // Stripe customer ID or similar
            $table->string('subscription_id')->nullable(); // For recurring donations
            $table->enum('country', ['US', 'UK', 'ZA'])->default('US');
            $table->text('notes')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            // Indexes for better query performance
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index('transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donations');
    }
};
