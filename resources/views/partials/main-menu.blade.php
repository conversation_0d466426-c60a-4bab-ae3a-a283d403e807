<nav class="main-menu" aria-label="Sidebar">
    <div class="menu-header">
        <div class="welcome-text">WELCOME</div>
        <div class="menu-toggle">
            <i class="fas fa-bars" aria-hidden="true"></i>
        </div>
    </div>

    <div class="logo-container">
        <img src="{{ asset('images/logo-cc-blue.png') }}" alt="Common Change">
    </div>


    <div class="menu-section">
        <div class="menu-item {{ request()->routeIs('dashboard.*') ? 'active' : '' }}">
            <a href="/dashboard" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-th-large" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Activity</span>
            </a>
        </div>
    </div>



    <div class="menu-section">
        <div class="section-title">GROUPS</div>

        <div class="menu-item group-list-trigger {{ request()->routeIs('group.*') ? 'active' : '' }}" style="cursor: pointer;">
            <div class="menu-icon">
                <i class="fas fa-users" aria-hidden="true"></i>
            </div>
            <span class="menu-text">{{ Auth::user()->get_current_group()->name }}</span>
            <i class="fas fa-chevron-down dropdown-icon" aria-hidden="true"></i>
        </div>

        <!-- Group list that appears below the row -->
        <div class="group-list" style="display: none; background: #f8f9fa; border-left: 3px solid var(--primary-purple); margin-left: 20px; padding: 10px 0;">
            @foreach(Auth::user()->groups()->orderBy('name')->get() as $group)
                <div class="group-list-item"
                     data-group-url="{{ route('group.set_current', ['id' => $group->id]) }}"
                     style="padding: 8px 16px; cursor: pointer; border-radius: 4px; margin: 2px 8px; }}">
                    <i class="fas fa-users" style="margin-right: 8px; font-size: 12px;"></i>
                    {{ $group->name }}
                    @if($group->id === Auth::user()->get_current_group()->id)
                        <i class="fas fa-check" style="float: right; margin-top: 2px;"></i>
                    @endif
                </div>
            @endforeach
        </div>

        <div class="menu-item {{ request()->routeIs('group.home') ? 'active' : '' }}">
            <a href="/group/{{ Auth::user()->get_current_group()->id }}" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-home" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Group Home</span>
            </a>
        </div>

        <div class="menu-item {{ request()->routeIs('finances.*') ? 'active' : '' }}">
            <div class="menu-icon">
                <i class="fas fa-hand-holding-heart" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Your Giving</span>
        </div>

        <div class="menu-item {{ request()->routeIs('issue.create') ? 'active' : '' }}">
            <a href="/group/{{ Auth::user()->get_current_group()->id }}/issue/create" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Make a Request</span>
            </a>
        </div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Ask for Help</span>
        </div>
        <div class="menu-item {{ request()->is('group/create') ? 'active' : '' }}">
            <a href="/group/create" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-users-cog" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Create New Group</span>
            </a>
        </div>
        <div class="menu-item {{ request()->is('mygroups') ? 'active' : '' }}">
            <a href="/mygroups" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-users-cog" aria-hidden="true"></i>
                </div>
                <span class="menu-text">All Groups</span>
            </a>
        </div>
    </div>

    <div class="menu-section">
        <div class="section-title">ACCOUNT</div>

        <div class="menu-item {{ request()->routeIs('v2.account.*') ? 'active' : '' }}">
            <a href="{{ route('v2.account.security') }}" style="color: inherit; text-decoration: none; display: flex; align-items: center; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-user" aria-hidden="true"></i>
                </div>
                <span class="menu-text">My Account</span>
            </a>
        </div>

        <div class="menu-item {{ request()->routeIs('v2.items.*') || request()->routeIs('v2.skills.*') ? 'active' : '' }}">
            <a href="{{ route('v2.items.index') }}" style="color: inherit; text-decoration: none; display: flex; align-items: center; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-box" aria-hidden="true"></i>
                </div>
                <span class="menu-text">My Items</span>
            </a>
        </div>

        <div class="menu-item {{ request()->routeIs('v2.account.invite') ? 'active' : '' }}">
            <a href="{{ route('v2.account.invite') }}" style="color: inherit; text-decoration: none; display: flex; align-items: center; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-share-alt" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Spread the Word</span>
            </a>
        </div>
    </div>

    <div class="menu-section">
        <div class="section-title">RESOURCES</div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-tools" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Tools & Guidance</span>
        </div>
    </div>

    @if(Auth::check() && Auth::user()->is_admin)
    <div class="menu-section">
        <div class="section-title">ADMIN</div>

        <div class="menu-item {{ request()->routeIs('admin.*') ? 'active' : '' }}">
            <div class="menu-icon">
                <i class="fas fa-user-shield" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Admin</span>
        </div>
    </div>
    @endif
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const groupList = document.querySelector('.group-list');
    const groupListTrigger = document.querySelector('.group-list-trigger');
    const dropdownIcon = document.querySelector('.dropdown-icon');
    const mainMenu = document.querySelector('.main-menu');
    //mainMenu.classList.add('expanded');

    // Make the entire row clickable for group list toggle
    if (groupListTrigger) {
        groupListTrigger.addEventListener('click', function(e) {
            e.stopPropagation();
            if (groupList.style.display === 'none' || groupList.style.display === '') {
                groupList.style.display = 'block';
                // Rotate the dropdown icon
                if (dropdownIcon) {
                    dropdownIcon.style.transform = 'rotate(180deg)';
                }
            } else {
                groupList.style.display = 'none';
                // Reset the dropdown icon
                if (dropdownIcon) {
                    dropdownIcon.style.transform = 'rotate(0deg)';
                }
            }
        });
    }

    // Note: Menu toggle functionality is handled by dashboard-menu.js

    // Handle group selection - reload page to update all group-related info
    document.querySelectorAll('.group-list-item').forEach(function(item) {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const url = this.getAttribute('data-group-url');
            // Reload the page after switching group to update all group-related links and info
            window.location.href = url + '?redirect_dashboard=1';
        });

        // Add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.style.backgroundColor.includes('var(--primary-purple)')) {
                this.style.backgroundColor = '#e9ecef';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.style.backgroundColor.includes('var(--primary-purple)')) {
                this.style.backgroundColor = '';
            }
        });
    });

    // Close group list when clicking outside
    document.addEventListener('click', function(e) {
        if (groupListTrigger && !groupListTrigger.contains(e.target) && !groupList.contains(e.target)) {
            groupList.style.display = 'none';
            // Reset the dropdown icon
            if (dropdownIcon) {
                dropdownIcon.style.transform = 'rotate(0deg)';
            }
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            mainMenu.classList.remove('expanded');
        }
    });
});
</script>
