@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
<link rel="stylesheet" href="{{ asset('css/v2-finances.css') }}">
<style>
/* Donation History specific styles */
.v2-donation-history-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.v2-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.v2-stat-card {
    background: var(--light-white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.v2-stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-purple);
    margin-bottom: 5px;
}

.v2-stat-label {
    font-size: 14px;
    color: var(--text-gray);
    font-weight: 500;
}

.v2-filters-section {
    background: var(--light-white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.v2-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.v2-donations-table {
    background: var(--light-white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.v2-table {
    width: 100%;
    border-collapse: collapse;
}

.v2-table th {
    background: var(--light-lavender);
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--primary-navy);
    border-bottom: 1px solid var(--border-gray);
}

.v2-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-gray);
    vertical-align: middle;
}

.v2-table tr:hover {
    background: var(--light-gray);
}

.v2-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.v2-badge-success {
    background: #D1FAE5;
    color: #065F46;
}

.v2-badge-warning {
    background: #FEF3C7;
    color: #92400E;
}

.v2-badge-error {
    background: #FEE2E2;
    color: #991B1B;
}

.v2-badge-secondary {
    background: #F3F4F6;
    color: #374151;
}

.v2-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-gray);
}

.v2-empty-icon {
    font-size: 48px;
    color: var(--border-gray);
    margin-bottom: 20px;
}

.v2-pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

/* Additional styling for better UX */
.text-muted {
    color: var(--text-gray) !important;
}

.v2-amount-cell {
    font-weight: 600;
    color: var(--primary-navy);
}

.v2-transaction-code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: var(--light-gray);
    padding: 2px 6px;
    border-radius: 3px;
}

/* Responsive design */
@media (max-width: 768px) {
    .v2-table {
        font-size: 14px;
    }

    .v2-table th,
    .v2-table td {
        padding: 10px 8px;
    }

    .v2-filters-grid {
        grid-template-columns: 1fr;
    }

    /* Hide less important columns on mobile */
    .v2-table th:nth-child(6),
    .v2-table td:nth-child(6),
    .v2-table th:nth-child(7),
    .v2-table td:nth-child(7) {
        display: none;
    }
}
</style>
@endpush

@section('title', 'Donation History')

@section('content')
<div class="v2-donation-history-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Donation History</h1>
    </div>

    <!-- Statistics Cards -->
    <div class="v2-stats-grid">
        <div class="v2-stat-card">
            <div class="v2-stat-value">${{ number_format($totalDonated, 2) }}</div>
            <div class="v2-stat-label">Total Donated</div>
        </div>
        <div class="v2-stat-card">
            <div class="v2-stat-value">{{ $totalDonations }}</div>
            <div class="v2-stat-label">Total Donations</div>
        </div>
        <div class="v2-stat-card">
            <div class="v2-stat-value">{{ $recurringDonations }}</div>
            <div class="v2-stat-label">Recurring Donations</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="v2-filters-section">
        <form method="GET" action="{{ route('v2.account.finances.donation-history') }}">
            <div class="v2-filters-grid">
                <div class="v2-form-group">
                    <label class="v2-form-label">Status</label>
                    <select name="status" class="v2-form-select">
                        <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Statuses</option>
                        <option value="completed" {{ $status === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="failed" {{ $status === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="cancelled" {{ $status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <label class="v2-form-label">Type</label>
                    <select name="type" class="v2-form-select">
                        <option value="all" {{ $type === 'all' ? 'selected' : '' }}>All Types</option>
                        <option value="one-time" {{ $type === 'one-time' ? 'selected' : '' }}>One-time</option>
                        <option value="recurring" {{ $type === 'recurring' ? 'selected' : '' }}>Recurring</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <label class="v2-form-label">Country</label>
                    <select name="country" class="v2-form-select">
                        <option value="all" {{ $country === 'all' ? 'selected' : '' }}>All Countries</option>
                        <option value="US" {{ $country === 'US' ? 'selected' : '' }}>United States</option>
                        <option value="UK" {{ $country === 'UK' ? 'selected' : '' }}>United Kingdom</option>
                        <option value="ZA" {{ $country === 'ZA' ? 'selected' : '' }}>South Africa</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <button type="submit" class="v2-btn v2-btn-primary">Filter</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Donations Table -->
    <div class="v2-donations-table">
        @if($donations->count() > 0)
            <table class="v2-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Amount</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Group</th>
                        <th>Payment Method</th>
                        <th>Transaction ID</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($donations as $donation)
                        <tr>
                            <td>{{ $donation->created_at->format('M j, Y') }}</td>
                            <td class="v2-amount-cell">{{ $donation->formatted_amount }}</td>
                            <td>
                                {{ ucfirst($donation->type) }}
                                @if($donation->frequency)
                                    <br><small class="text-muted">({{ ucfirst($donation->frequency) }})</small>
                                @endif
                            </td>
                            <td>
                                <span class="v2-badge {{ $donation->status_badge_class }}">
                                    {{ ucfirst($donation->status) }}
                                </span>
                            </td>
                            <td>
                                @if($donation->group)
                                    {{ $donation->group->name }}
                                @else
                                    <span class="text-muted">Common Change</span>
                                @endif
                            </td>
                            <td>{{ ucfirst(str_replace('_', ' ', $donation->payment_method)) }}</td>
                            <td>
                                @if($donation->transaction_id)
                                    <span class="v2-transaction-code">{{ Str::limit($donation->transaction_id, 20) }}</span>
                                @else
                                    <span class="text-muted">—</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="v2-empty-state">
                <div class="v2-empty-icon">
                    <i class="fas fa-hand-holding-heart"></i>
                </div>
                <h3>No donations found</h3>
                <p>You haven't made any donations yet, or no donations match your current filters.</p>
                <a href="{{ route('v2.account.finances') }}" class="v2-btn v2-btn-primary">Make a Donation</a>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($donations->hasPages())
        <div class="v2-pagination">
            {{ $donations->appends(request()->query())->links() }}
        </div>
    @endif
</div>
@endsection
