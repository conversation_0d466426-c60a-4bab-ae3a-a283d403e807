<?php

namespace App\Http\Controllers;

use App\Http\Requests\CompleteRegistrationRequest;
use App\Http\Requests\RegisterUserRequest;
use App\Models\Address;
use App\Models\CsvHelper;
use App\Models\Group;
use App\Models\GroupInvite;
use App\Models\MobileAppAccount;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\RateLimiter;

class UserController extends Controller
{
    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): \Illuminate\View\View
    {
        // Process request input:
        $input = $request->all();
        $search = $input['search'] ?? '';
        $sort = $input['sort'] ?? '';

        // Base query:
        $query = User::with('address', 'default_group');

        // Handle search / sort:
        if (! empty($search)) {
            $query->searchNameEmail($search);
        }

        switch ($sort) {
            case 'revalpha':
                $query->orderBy('firstname', 'desc')->orderBy('lastname', 'desc');
                break;
            case 'state':
                $query->select('users.*')
                    ->leftJoin('addresses as addr', function ($join) {
                        $join->on('users.id', '=', 'addr.addressable_id');
                        $join->on('addr.addressable_type', 'LIKE', DB::raw("'User'"));
                    })->orderBy('addr.state')->orderBy('firstname')->orderBy('lastname');
                break;
            case 'country':
                $query->select('users.*')
                    ->leftJoin('addresses as addr', function ($join) {
                        $join->on('users.id', '=', 'addr.addressable_id');
                        $join->on('addr.addressable_type', 'LIKE', DB::raw("'User'"));
                    })->orderBy('addr.country')->orderBy('firstname')->orderBy('lastname');
                break;
            default:
                $query->orderBy('firstname')->orderBy('lastname');
                break;
        }

        $users = $query->paginate(15)->appends($request->except('page'));

        // This page displays users in triples, so let's get that sorted here:
        $triple_users = [];
        $key = 0;
        foreach ($users as $user) {
            $triple_key = floor($key / 3);
            switch ($key % 3) {
                case 0:
                    $triple_users[$triple_key] = [];
                    $class = 'padd-four';
                    break;
                case 1:
                    $class = 'no-padding';
                    break;
                case 2:
                    $class = 'padd-five';
                    break;
            }
            $user['triple_class'] = $class;
            $triple_users[$triple_key][] = $user;
            $key++;
        }

        return view('users.index')->withUsers($triple_users)->withPUsers($users)->withInput($input);
    }

    public function admin_index(Request $request)
    {
        // Process request input:
        $input = $request->all();
        $group_id = $input['group'] ?? 0;
        $search_string = $input['search'] ?? '';
        $sort = $input['sort'] ?? '';

        // Base query:
        $query = User::withTrashed()->with('address', 'default_group');

        // Handle filter / search:
        if ($group_id) {
            $query->hasGroup($group_id);
        }
        if (! empty($search_string)) {
            $query->searchNameEmail($search_string);
        }

        // Handle sorting:
        switch ($sort) {
            case 'joined':
                $query->orderBy('created_at', 'desc');
                break;
            case 'group':
                $query->select('users.*')
                    ->leftJoin('groups as dg', 'users.default_group_id', '=', 'dg.id')
                    ->orderBy('dg.name')->orderBy('firstname')->orderBy('lastname');
                break;
            default:
                $query->orderBy('firstname')->orderBy('lastname');
                break;
        }

        if ($request->has('output_csv')) {
            $users = $query->get();  // don't paginate for csv
            $results = [];
            $blank_address = new Address;  // in case a user doesn't have an address

            foreach ($users as $user) {
                $row = [
                    'user_id' => $user->id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'email' => $user->email,
                    'default_group' => (isset($user->default_group)) ? $user->default_group->name : '',
                ];
                $address = $user->address ?: $blank_address;
                $results[] = $row + $address->toArray();
            }

            return CsvHelper::response_stream($results, 'cc_users');
        } else {
            $paged_users = $query->paginate(50)->appends($request->except('page'));

            return view('users.admin_index')->withUsers($paged_users)->withInput($input);
        }

    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): RedirectResponse
    {
        return redirect('login');
    }

    /**
     * Store a newly created user.
     */
    public function store(RegisterUserRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $this->user->fill($data);
        $this->user->share_email = (isset($data['share_email']));
        $this->user->save();
        $this->user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]); // make everyone a member of the Common Change Network
        Auth::login($this->user);

        // See if either their email, or their session token matches an invite;
        GroupInvite::checkTokenAndEmail();

        return redirect('/sendvalidation');
    }

    /**
     * Display the specified user.
     */
    public function show(int $id): JsonResponse
    {
        if (! Auth::User()->is_admin) {
            abort(404);
        } else {
            $user = User::withTrashed()->find($id);

            return Response::json($user, $status = 200, $headers = [], $options = JSON_PRETTY_PRINT);
        }
    }

    /**
     * Show the form for editing the specified user.
     *
     * @param  int  $id
     */
    public function edit(): \Illuminate\View\View
    {
        return view('account.profile')->withUser(Auth::User());
    }

    public function edit_wizard(): \Illuminate\View\View
    {
        // $user = Auth::User();
        // return var_dump($user->toArray());
        return view('wizard.account')->withUser(Auth::User());
    }

    private function do_update(Request $request, User $user)
    {
        // dd(json_encode($request->all()));
        $address = $user->address ?: new Address;
        $address->fill($request->input('address'));
        $user->fill($request->only('firstname', 'lastname', 'email', 'facebook', 'twitter', 'default_email_notification'));

        $errors = new MessageBag;
        if (! $address->isValid()) {
            $errors->merge($address->errors);
        }
        if (! $user->isValid()) {
            $errors->merge($user->errors);
        }

        if ($errors->count() == 0) {
            if ($user->address) {
                $address->save();
            } else {
                $user->address()->save($address);
            }

            $user->share_email = ($request->input('share_email') !== null);

            // file is originally uploaded to /tmp/randpath/randname
            if ($request->hasFile('profile_pic')) {
                $file = $request->file('profile_pic');
                // return ($file->isValid()) ? 'true' : 'false';
                if (! $file->isValid()) {
                    return 'invalid file';
                }
                // return $file->getRealPath().'/'.$file->getFilename();
                $file2 = $file->move(public_path().'/pix');
                $user->profile_pic = $file2->getFilename();
            }
        }

        return $errors;
    }

    /**
     * Update the specified user in storage.
     *
     * @var User
     */
    public function update(Request $request): RedirectResponse
    {
        $user = $request->user();
        $errors = $this->do_update($request, $user);

        if ($errors->count()) {
            return back()->withInput()->withErrors($errors)->withAlerts(['danger' => 'Changes not saved.  See errors']);
        }

        $user->save();

        return back()->withAlerts(['success' => 'Changes Saved']);
    }

    public function update_wizard(Request $request)
    {
        $user = $request->user();
        $errors = $this->do_update($request, $user);

        if ($errors->count()) {
            return back()->withInput()->withErrors($errors)->withAlerts(['danger' => 'Changes not saved.  See errors']);
        }

        $user->finishedWizardSection('account');
        $user->save();

        return redirect()->route($user->currentWizardRoute())->withAlerts(['success' => 'Changes Saved']);
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(int $id): Response
    {
        // User::destroy($id);
    }

    public function sendValidation(): RedirectResponse
    {
        $user = Auth::User();
        if ($user->email_valid) {
            return redirect('/');
        }

        // make sure the token is unique:
        do {
            $token = Str::uuid();
        } while (User::where('token', $token)->count());

        $user->token = $token;
        $user->save();

        $mail_data = [
            'token' => $token,
            'title' => 'Please confirm your Email Address',
        ];
        Mail::send(['emails.register.validate', 'emails.register.validate-text'], $mail_data, function ($message) use ($user) {
            $message->to($user->email, $user->firstname.' '.$user->lastname);
            $message->bcc('<EMAIL>');
            $message->subject('Confirm Email for CommonChange.com');
        });

        return redirect('/validate')->with('sent', true);
    }

    public function confirmValidation($token): RedirectResponse
    {
        $user = User::where('token', $token)->first();

        if (! isset($user)) {
            alert()->warning('Invalid Token', 'Your token is no longer valid. You can request a new confirmation email below.');

            return redirect('/validate')->with('invalid', true);
        }

        $user->token = null;
        $user->email_valid = true;
        $user->save();

        alert()->success('Email Verified', 'Your email has been successfully verified!');

        return redirect('/wizard');
    }

    public function manualValidation($user_id)
    {
        $user = User::findOrFail($user_id);

        if (! $user->email_valid) {
            $user->token = null;
            $user->email_valid = true;
            $user->save();
        }

        return back()->withAlerts(['success' => 'User email validated.']);
    }

    public function invitePage(Request $request): \Illuminate\View\View
    {
        return view('account.invite')->with('old_input', $request->old());
    }

    private function invite_to_group(Request $request, $emails)
    {
        $group = Group::findOrFail($request->input('group_id'));
        $group->load('members');
        $me = $group->members->find(Auth::User());

        // $permissions_matrix[group_setting][user_role]:
        $permissions_matrix = [
            'member' => ['member' => true,  'admin' => true,  'owner' => true],
            'admin' => ['member' => false, 'admin' => true,  'owner' => true],
            'owner' => ['member' => false, 'admin' => false, 'owner' => true],
        ];
        // error out if they don't belong to the group or don't have permission to invite
        if (! (isset($me) && $permissions_matrix[$group->inviter][$me->pivot->role])) {
            return back()->withInput()->withAlerts(['danger' => "You don't have permission to invite members to ".$group->name]);
        }

        // Separate the submitted people into users and non-users:
        $existing_users = [];
        $non_users = [];
        foreach ($emails as $email) {
            $u = User::where('email', $email['email'])->first();
            if (isset($u)) {
                $existing_users[] = $u;
            } else {
                $non_users[] = $email;
            }
        }

        // Add existing users' ids to the group_invites table if they don't already belong to the group:
        foreach ($existing_users as $u) {
            $added = GroupInvite::inviteUser($u, $group);
        }

        if (count($non_users)) {
            // Add non-users to the group_invites table and assign them a code
            foreach ($non_users as $key => $invitee) {
                $non_users[$key]['token'] = GroupInvite::inviteNonUser($invitee['email'], $group);
            }

            // Send a group invite email to the non_users;
            $mail_data = [
                'title' => 'Join CommonChange!',
                'user' => $me,
                'group' => $group,
                'comment' => $request->input('message'),
            ];

            foreach ($non_users as $email) {
                try {
                    $mail_data['token'] = $email['token'];

                    \Log::info('Sending group invite email', [
                        'to' => $email['email'],
                        'name' => $email['firstname'].' '.$email['lastname'],
                        'group' => $group->name,
                        'token' => $email['token']
                    ]);

                    Mail::send(['emails.group_invite_nonuser', 'emails.group_invite_nonuser-text'], $mail_data, function ($message) use ($email) {
                        $message->to($email['email'], $email['firstname'].' '.$email['lastname']);
                        $message->subject('You have an invite waiting for you on Common Change, '.$email['firstname']);
                    });

                    \Log::info('Group invite email sent successfully', ['to' => $email['email']]);
                } catch (\Exception $e) {
                    \Log::error('Failed to send group invite email', [
                        'to' => $email['email'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Send <NAME_EMAIL>
            $admin_mail_data = [
                'invitees' => $non_users,
                'inviter' => $me,
                'comment' => $request->input('message'),
                'group' => $group,
            ];

            try {
                \Log::info('Sending admin notification email for group invites');

                Mail::send(['emails.group_invite_sent', 'emails.group_invite_sent-text'], $admin_mail_data, function ($message) {
                    $message->to('<EMAIL>', 'CommonChange Invites');
                    $message->subject('New invites sent');
                });

                \Log::info('Admin notification email sent successfully');
            } catch (\Exception $e) {
                \Log::error('Failed to send admin notification email', ['error' => $e->getMessage()]);
            }
        }

        $successMessage = 'Invites have been sent!';
        if (count($non_users) > 0) {
            $successMessage .= ' We sent ' . count($non_users) . ' invitation email(s). If your friends don\'t receive the email within a few minutes, please ask them to check their spam/junk folder.';
        }

        return redirect('/account/invite')->withAlerts(['success' => $successMessage]);
    }

    private function invite_to_site(Request $request, $emails)
    {
        // Separate the submitted people into users and non-users:
        $existing_users = [];
        $non_users = [];
        foreach ($emails as $email) {
            $u = User::where('email', $email['email'])->first();
            if (isset($u)) {
                $existing_users[] = $u;
            } else {
                $non_users[] = $email;
            }
        }

        // Send email only to those who don't already have an account:
        $mail_data = [
            'title' => 'Join CommonChange!',
            'user' => Auth::User(),
            'comment' => $request->input('message'),
        ];

        foreach ($non_users as $email) {
            try {
                \Log::info('Sending site invite email', [
                    'to' => $email['email'],
                    'name' => $email['firstname'].' '.$email['lastname']
                ]);

                Mail::send(['emails.invite', 'emails.invite-text'], $mail_data, function ($message) use ($email) {
                    $message->to($email['email'], $email['firstname'].' '.$email['lastname']);
                    $message->subject('Join CommonChange!');
                });

                \Log::info('Site invite email sent successfully', ['to' => $email['email']]);
            } catch (\Exception $e) {
                \Log::error('Failed to send site invite email', [
                    'to' => $email['email'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        $successMessage = 'Invites have been sent!';
        if (count($non_users) > 0) {
            $successMessage .= ' We sent ' . count($non_users) . ' invitation email(s). If your friends don\'t receive the email within a few minutes, please ask them to check their spam/junk folder.';
        }
        if (count($existing_users) > 0) {
            $successMessage .= ' Note: ' . count($existing_users) . ' of the email addresses you entered already have CommonChange accounts.';
        }

        return redirect('/account/invite')->withAlerts(['success' => $successMessage]);
    }

    public function sendInvite(Request $request)
    {
        // Add logging to debug the invite process
        \Log::info('Invite process started', [
            'user_id' => Auth::id(),
            'invite_to' => $request->input('invite_to'),
            'emails_count' => is_array($request->input('email')) ? count($request->input('email')) : 0
        ]);

        $is_group_invite = ($request->input('invite_to') == 'group');
        $emails = $request->input('email');

        $rules = [
            'firstname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'lastname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'email' => 'required|email',
        ];

        // Validate input
        $errors = new MessageBag;

        if (is_array($emails) || is_object($emails)) {
            foreach ($emails as $email) {
                $validation = Validator::make($email, $rules);
                if (! $validation->passes()) {
                    $errors = $errors->merge($validation->messages());
                }
            }
            if ($errors->count()) {
                \Log::error('Invite validation failed', ['errors' => $errors->toArray()]);
                return back()->withInput()->withErrors($errors);
            }
        } else {
            \Log::error('No emails provided or invalid format');
            return back()->withInput()->withErrors(['emails' => 'No emails provided or invalid format.']);
        }

        return ($is_group_invite) ? $this->invite_to_group($request, $emails) : $this->invite_to_site($request, $emails);
    }

    public function setDefaultGroup($group_id)
    {
        $user = Auth::User();
        $group = Group::with('members')->findOrFail($group_id);

        if (! $group->members->contains($user)) {
            return back()->withAlerts(['danger' => "You're not a member of that group."]);
        }

        $user->default_group()->associate($group);
        $user->save();

        return back()->withAlerts(['success' => 'Default group set']);

    }

    public function toggleSuspended($user_id)
    {
        $admin = Auth::User();
        if (! $admin->is_admin) {
            abort(404);
        }

        if ((int) $user_id == $admin->id) {
            return back()->withAlerts(['warning' => "You can't do that to yourself"]);
        }

        $user = User::find($user_id);

        if (! isset($user)) {
            return redirect()->route('dashboard')->withAlerts(['danger' => 'User not found']);
        }

        $user->suspended = ! $user->suspended;
        $user->save();
        $message = ($user->suspended) ? 'User suspended.' : 'User reactivated.';

        return back()->withAlerts(['info' => $message]);
    }

    public function toggleMobile($user_id)
    {
        $admin = Auth::User();
        if (! $admin->is_admin) {
            abort(404);
        }

        $user = User::find($user_id);

        if (! isset($user)) {
            return redirect()->route('dashboard')->withAlerts(['danger' => 'User not found']);
        }

        $user->can_use_mobile_app = ! $user->can_use_mobile_app;
        $user->save();
        $message = ($user->can_use_mobile_app) ? 'User can now use mobile app.' : 'User can no longer use mobile app.';

        return back()->withAlerts(['info' => $message]);
    }

    // Collects the user's onesignal id from the app when they login:
    public function set_mobile(Request $request): JsonResponse
    {
        $user = Auth::User();

        if ($request->has('oneSignalUserId')) {
            $one_signal_id = $request->input('oneSignalUserId');
            session()->put('one_signal_id', $one_signal_id);

            if (isset($user)) {
                if ($user->can_use_mobile_app) {
                    $mobile_acct = MobileAppAccount::where('one_signal_id', $one_signal_id)->first();
                    if (! isset($mobile_acct)) {
                        $mobile_acct = new MobileAppAccount;
                        $mobile_acct->one_signal_id = $one_signal_id;
                    }

                    $mobile_acct->user_id = $user->id;
                    $mobile_acct->data = $request->all();
                    $mobile_acct->logged_in = true;
                    $mobile_acct->save();

                    $log_data = $request->only('oneSignalUserId');
                    $log_data['user_id'] = $user->id;
                    Log::info('Acquiring mobile app data:', $log_data);

                    $response = [
                        'status' => 'success',
                    ];
                    $code = 200;
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => 'User not authorized to use mobile app',
                    ];
                    $code = 403;
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'Not logged in.',
                ];
                $code = 401;
            }
        } else {
            $response = [
                'status' => 'error',
                'Request missing oneSignalUserId.',
            ];
            $code = 400;
        }

        return Response::json($response, $code);
    }

    /**
     * Send OTP to the provided email address.
     */
    public function sendOtp(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $email = $request->email;

        // Check if email already exists
        $existingUser = User::withTrashed()->where('email', $email)->first();

        if ($existingUser) {
            if ($existingUser->trashed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This account has been deactivated. Please contact support.',
                    'errors' => ['email' => 'This account has been deactivated.'],
                ], 422);
            }

            return response()->json([
                'success' => false,
                'message' => 'This email is already registered. Please login instead.',
                'redirect' => route('auth.login'),
                'errors' => ['email' => 'Email already registered.'],
            ], 422);
        }

        // Generate a 6-digit OTP
        $otp = sprintf('%06d', mt_rand(0, 999999));

        // Store OTP in session
        Session::put('otp_email', $email);
        Session::put('otp_code', $otp);
        Session::put('otp_created_at', now());
        Session::put('email_verified', false);

        // Send OTP email
        $mail_data = [
            'otp' => $otp,
            'title' => 'Your Email Verification Code',
        ];

        Mail::send(['emails.otp.verify', 'emails.otp.verify-text'], $mail_data, function ($message) use ($email) {
            $message->to($email);
            $message->subject('Your Verification Code for CommonChange.com');
        });

        return response()->json([
            'success' => true,
            'message' => 'Verification code sent successfully',
            'redirect' => route('email.verify.show', ['email' => $email]),
        ]);
    }

    /**
     * Show the email verification page.
     */
    public function showVerifyEmail(string $email)
    {
        // Check if there's an OTP in session for this email
        if (Session::get('otp_email') !== $email) {
            return redirect()->route('signup')->with('error', 'Please start the verification process again.');
        }

        return view('register.verify_email', ['email' => $email]);
    }

    /**
     * Verify the submitted OTP.
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        $code = $request->code;

        // Get stored OTP from session
        $storedOtp = Session::get('otp_code');
        $email = Session::get('otp_email');
        $createdAt = Session::get('otp_created_at');

        // Check if OTP is expired (15 minutes)
        if (now()->diffInMinutes($createdAt) > 15) {
            // Clear expired OTP data
            Session::forget(['otp_code', 'otp_email', 'otp_created_at']);

            return response()->json([
                'success' => false,
                'message' => 'Verification code has expired. Please request a new one.',
                'errors' => ['code' => 'Verification code has expired.'],
            ], 422);
        }

        // Add rate limiting for OTP verification
        if (RateLimiter::tooManyAttempts('verify-otp:'.$request->ip(), 5)) {
            $seconds = RateLimiter::availableIn('verify-otp:'.$request->ip());
            return response()->json([
                'success' => false,
                'message' => "Too many verification attempts. Please try again in {$seconds} seconds.",
                'errors' => ['code' => "Too many verification attempts. Please try again in {$seconds} seconds."],
            ], 429);
        }

        // Verify the code
        if ($code === $storedOtp) {
            // Clear rate limiter on successful verification
            RateLimiter::clear('verify-otp:'.$request->ip());

            // Mark email as verified in session
            Session::put('email_verified', true);
            Session::put('verified_email', $email);

            // Clear OTP data after successful verification
            Session::forget(['otp_code', 'otp_created_at']);

            return response()->json([
                'success' => true,
                'message' => 'Email verified successfully',
                'redirect' => route('email.confirmed'),
            ]);
        }

        // Increment rate limiter on failed attempt
        RateLimiter::hit('verify-otp:'.$request->ip());

        return response()->json([
            'success' => false,
            'message' => 'Invalid verification code. Please try again.',
            'errors' => ['code' => 'Invalid verification code.'],
        ], 422);
    }

    /**
     * Resend the verification email.
     */
    public function resendVerification(): JsonResponse
    {
        $email = Session::get('otp_email');

        if (! $email) {
            return response()->json([
                'success' => false,
                'message' => 'No email found for verification',
                'redirect' => route('signup'),
            ], 422);
        }

        // Generate a new OTP
        $otp = sprintf('%06d', mt_rand(0, 999999));

        // Update session
        Session::put('otp_code', $otp);
        Session::put('otp_created_at', now());

        // Send OTP email
        $mail_data = [
            'otp' => $otp,
            'title' => 'Your Email Verification Code',
        ];

        Mail::send(['emails.otp.verify', 'emails.otp.verify-text'], $mail_data, function ($message) use ($email) {
            $message->to($email);
            $message->subject('Your Verification Code for CommonChange.com');
        });

        return response()->json([
            'success' => true,
            'message' => 'Verification code resent successfully',
        ]);
    }

    /**
     * Show the registration start form.
     */
    public function showRegisterStart(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
    {
        // Log all session data for debugging
        \Log::info('Session data in showRegisterStart', [
            'all_session_data' => session()->all(),
            'email_verified' => session('email_verified'),
            'verified_email' => session('verified_email'),
            'verified_password' => session('verified_password') ? 'exists' : 'not set',
            'oauth_email' => session('oauth_email'),
        ]);

        // Check if coming from OAuth (Google, etc.)
        if (session('oauth_email')) {
            $email = session('oauth_email');

            // For OAuth logins, we consider the email already verified
            return view('register.start', [
                'email' => $email,
                'oauth_login' => true,
                'oauth_firstname' => session('oauth_firstname'),
                'oauth_lastname' => session('oauth_lastname'),
                'states' => app('App\Models\LocationHelper')->subdivisionsForSelect('US'),
                'countries' => app('App\Models\LocationHelper')->countriesForSelect(),
            ]);
        }

        // Check if email is verified in session
        if (! session('email_verified')) {
            \Log::warning('Email not verified, redirecting to signup');

            return redirect()->route('signup')
                ->withErrors(['email' => 'Please verify your email first.']);
        }

        // Check if password has been set
        if (! session('verified_password')) {
            \Log::warning('Password not set, redirecting to create-password');

            return redirect()->route('password.create.show');
        }

        $email = session('verified_email');

        return view('register.start', [
            'email' => $email,
            'states' => app('App\Models\LocationHelper')->subdivisionsForSelect('US'),
            'countries' => app('App\Models\LocationHelper')->countriesForSelect(),
        ]);
    }

    /**
     * Complete the registration process after email verification.
     */
    public function completeRegistration(CompleteRegistrationRequest $request): RedirectResponse
    {
        // Verify that email was verified in the session
        if (!Session::get('email_verified') || Session::get('verified_email') !== $request->email) {
            Log::warning('Email verification check failed', [
                'email_verified' => Session::get('email_verified'),
                'verified_email' => Session::get('verified_email'),
                'request_email' => $request->email,
            ]);

            return redirect()->route('signup')
                ->withErrors(['email' => 'Please verify your email first.']);
        }

        try {
            // Check for existing user with this email (including soft-deleted users)
            $existingUser = User::withTrashed()->where('email', $request->email)->first();

            if ($existingUser) {
                if ($existingUser->trashed()) {
                    Log::warning('Attempt to register with soft-deleted user email', [
                        'email' => $request->email,
                    ]);

                    return redirect()->route('auth.login')
                        ->withErrors(['email' => 'This account has been deactivated. Please contact support.'])
                        ->withInput();
                }

                Log::warning('Duplicate user registration attempt', [
                    'email' => $request->email,
                ]);

                return redirect()->route('auth.login')
                    ->withErrors(['email' => 'This email is already registered. Please login instead.'])
                    ->withInput();
            }

            // Use database transaction to ensure all related data is saved or rolled back together
            DB::beginTransaction();

            try {
                // Create the user
                $user = new User;
                $user->firstname = $request->firstname;
                $user->lastname = $request->lastname;
                $user->email = $request->email;
                $user->password = Session::get('verified_password');
                $user->email_valid = true;
                $user->email_verified_at = now();
                $user->save();

                // Handle profile picture upload
                if ($request->hasFile('profile_pic')) {
                    $file = $request->file('profile_pic');
                    if ($file->isValid()) {
                        // Generate a unique filename with timestamp to prevent overwriting
                        $filename = time() . '_' . uniqid('profile_') . '.' . $file->getClientOriginalExtension();

                        // Move the file to the public/pix directory
                        $file->move(public_path().'/pix', $filename);

                        // Update the user's profile picture
                        $user->profile_pic = $filename;
                        $user->save();
                    } else {
                        DB::rollBack();
                        return redirect()->back()
                            ->withErrors(['profile_pic' => 'The uploaded file is invalid.'])
                            ->withInput();
                    }
                }

                // Make user a member of the Common Change Network (group ID 1)
                $user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]);

                // Commit the transaction
                DB::commit();

                // Clear all verification session data
                Session::forget([
                    'otp_email',
                    'otp_code',
                    'otp_created_at',
                    'email_verified',
                    'verified_email',
                    'verified_password'
                ]);

                // Log the user in
                Auth::login($user);

                // Check for group invites
                GroupInvite::checkTokenAndEmail();

                // Log successful registration
                Log::info('User registered successfully', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);

                return redirect()->route('dashboard.new', ['user_id' => $user->id])
                    ->withAlerts(['success' => 'Your account has been created successfully!']);

            } catch (\Exception $e) {
                // Something went wrong, rollback the transaction
                DB::rollBack();

                Log::error('Error during user registration transaction', [
                    'email' => $request->email,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->back()
                    ->withErrors(['database' => 'An error occurred while creating your account. Please try again later.'])
                    ->withInput();
            }

        } catch (\Illuminate\Database\QueryException $e) {
            // Check if it's a duplicate entry error
            if ($e->errorInfo[1] == 1062) {
                Log::error('Duplicate key error during registration', [
                    'email' => $request->email,
                    'error' => $e->getMessage(),
                ]);

                return redirect()->route('auth.login')
                    ->withErrors(['email' => 'This email is already registered. Please login instead.'])
                    ->withInput();
            }

            // For other database errors
            Log::error('Database error during registration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withErrors(['database' => 'A database error occurred. Please try again later.'])
                ->withInput();
        } catch (\Exception $e) {
            // Catch any other exceptions
            Log::error('Unexpected error during registration', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'An unexpected error occurred. Please try again later.'])
                ->withInput();
        }
    }

    /**
     * Show the email confirmation page after successful verification.
     */
    public function showEmailConfirmed()
    {
        // Check if email is verified in session
        if (!Session::get('email_verified')) {
            return redirect()->route('signup')
                ->withErrors(['email' => 'Please verify your email first.']);
        }

        return view('register.email_confirmed');
    }

    /**
     * Show the create password page.
     */
    public function showCreatePassword()
    {
        // Check if email is verified in session
        if (!Session::get('email_verified')) {
            return redirect()->route('signup')
                ->withErrors(['email' => 'Please verify your email first.']);
        }

        $email = Session::get('verified_email');

        return view('register.create_password', ['email' => $email]);
    }

    /**
     * Handle the password creation form submission.
     */
    public function createPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        // Verify that email was verified in the session
        if (!Session::get('email_verified') || Session::get('verified_email') !== $request->email) {
            Log::warning('Email verification check failed during password creation', [
                'email_verified' => Session::get('email_verified'),
                'verified_email' => Session::get('verified_email'),
                'request_email' => $request->email,
            ]);

            return redirect()->route('signup')
                ->withErrors(['email' => 'Please verify your email first.']);
        }

        try {
            // Check for existing user with this email (including soft-deleted users)
            $existingUser = User::withTrashed()->where('email', $request->email)->first();

            if ($existingUser) {
                if ($existingUser->trashed()) {
                    Log::warning('Attempt to create password for soft-deleted user email', [
                        'email' => $request->email,
                    ]);

                    return redirect()->route('auth.login')
                        ->withErrors(['email' => 'This account has been deactivated. Please contact support.'])
                        ->withInput();
                }

                Log::warning('Attempt to create password for existing user', [
                    'email' => $request->email,
                ]);

                return redirect()->route('auth.login')
                    ->withErrors(['email' => 'This email is already registered. Please login instead.'])
                    ->withInput();
            }

            // Store password in session for later use in registration
            Session::put('verified_password', bcrypt($request->password));

            // Redirect to the registration form
            return redirect()->route('register.start')
                ->withAlerts(['success' => 'Password set successfully. Please complete your account details.']);

        } catch (\Exception $e) {
            Log::error('Error during password creation', [
                'email' => $request->email,
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'An error occurred. Please try again later.'])
                ->withInput();
        }
    }
}
