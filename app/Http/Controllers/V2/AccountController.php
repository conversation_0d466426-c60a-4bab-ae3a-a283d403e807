<?php

namespace App\Http\Controllers\V2;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Address;
use App\Models\Group;
use App\Models\Donation;
use App\Models\StripeMetadata;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;

class AccountController extends Controller
{
    private $stripe;

    public function __construct()
    {
        $stripeSecretKey = env('STRIPE_SECRET_KEY');

        // Check if the Stripe key is set before initializing the client
        if ($stripeSecretKey) {
            $this->stripe = new \Stripe\StripeClient($stripeSecretKey);
        } else {
            // Log the missing key
            \Log::error('Stripe secret key is not set in environment variables');
            // Initialize with empty config to avoid errors when listing routes
            $this->stripe = null;
        }
    }

    /**
     * Display the account index page (redirects to security by default)
     */
    public function index()
    {
        return redirect()->route('v2.account.security');
    }

    /**
     * Display the account security page
     */
    public function security()
    {
        $user = Auth::user();
        return view('v2.accounts.security', compact('user'));
    }

    /**
     * Update account security settings
     */
    public function updateSecurity(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email,' . $user->id,
            'new_password' => 'nullable|min:8|confirmed',
            'profile_pic' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Update email
        $user->email = $request->email;

        // Update password if provided
        if ($request->filled('new_password')) {
            $user->password = $request->new_password;
        }

        // Handle profile picture upload
        if ($request->hasFile('profile_pic')) {
            $file = $request->file('profile_pic');
            if ($file->isValid()) {
                $file2 = $file->move(public_path().'/pix');
                $user->profile_pic = $file2->getFilename();
            }
        }

        $user->save();

        return back()->with('success', 'Security settings updated successfully.');
    }

    /**
     * Display the contact info page
     */
    public function contact()
    {
        $user = Auth::user();
        return view('v2.accounts.contact', compact('user'));
    }

    /**
     * Update contact information
     */
    public function updateContact(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'firstname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'lastname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'address.addr1' => 'nullable|string|max:255',
            'address.addr2' => 'nullable|string|max:255',
            'address.city' => 'nullable|string|max:255',
            'address.state' => 'nullable|string|max:255',
            'address.postcode' => 'nullable|string|max:20',
            'address.country' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Update user basic info
        $user->fill($request->only('firstname', 'lastname'));

        // Update address
        $address = $user->address ?: new Address;
        $address->fill($request->input('address', []));

        if ($user->address) {
            $address->save();
        } else {
            $user->address()->save($address);
        }

        $user->save();

        return back()->with('success', 'Contact information updated successfully.');
    }

    /**
     * Display the social media page
     */
    public function social()
    {
        $user = Auth::user();
        return view('v2.accounts.social', compact('user'));
    }

    /**
     * Update social media settings
     */
    public function updateSocial(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'facebook' => 'nullable|url',
            'twitter' => 'nullable|url',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user->fill($request->only('facebook', 'twitter'));
        $user->save();

        return back()->with('success', 'Social media settings updated successfully.');
    }

    /**
     * Display the notifications page
     */
    public function notifications()
    {
        $user = Auth::user();
        return view('v2.accounts.notifications', compact('user'));
    }

    /**
     * Update notification preferences
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'default_email_notification' => 'required|in:daily_digest,daily_per_discussion,per_comment,none',
            'share_email' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user->default_email_notification = $request->default_email_notification;
        $user->share_email = $request->has('share_email');
        $user->save();

        return back()->with('success', 'Notification preferences updated successfully.');
    }

    /**
     * Display the finances page
     */
    public function finances()
    {
        $user = Auth::user();
        $address = $user->address;
        $country_code = $address ? $address->country : null;

        // Determine active tab based on user's country
        $activeTab = 'us'; // default
        switch ($country_code) {
            case 'ZA':
            case 'za':
                $activeTab = 'sa';
                break;
            case 'GB':
            case 'gb':
                $activeTab = 'uk';
                break;
            case 'US':
            case 'us':
            default:
                $activeTab = 'us';
                break;
        }

        // Get user's groups for the dropdown
        $userGroups = $user->groups()->orderBy('name')->get();

        return view('v2.finances.index', compact('user', 'activeTab', 'userGroups'));
    }

    /**
     * Search groups for AJAX dropdown
     */
    public function searchGroups(Request $request)
    {
        $user = Auth::user();
        $search = $request->get('q', '');

        // Get groups the user is a member of or can view
        $query = Group::notSuspended()->has('members', '>', 1);

        if (!$user->is_admin) {
            $query = $query->where(function ($q) use ($user) {
                return $q->notSecret()
                    ->orWhereHas('members', function($subQ) use ($user) {
                        $subQ->where('users.id', $user->id);
                    });
            });
        }

        // Apply search filter
        if (!empty($search)) {
            $query = $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('shortname', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        $groups = $query->orderBy('name')
                       ->limit(20)
                       ->get(['id', 'name', 'shortname', 'city', 'state', 'country']);

        // Format for Select2
        $results = $groups->map(function ($group) {
            $location = collect([$group->city, $group->state, $group->country])
                       ->filter()
                       ->implode(', ');

            return [
                'id' => $group->id,
                'text' => $group->name,
                'shortname' => $group->shortname,
                'location' => $location
            ];
        });

        return response()->json([
            'results' => $results,
            'pagination' => ['more' => false]
        ]);
    }

    /**
     * Process donation payment
     */
    public function processDonation(Request $request)
    {
        $rules = [
            'donation_type' => 'required|in:one-time,recurring',
            'donation_frequency' => 'required_if:donation_type,recurring|nullable|in:weekly,monthly',
            'amount_commonchange' => 'nullable|numeric|min:0',
            'amount_group' => 'nullable|numeric|min:0',
        ];

        // Clear donation_frequency if donation_type is one-time
        if ($request->input('donation_type') === 'one-time') {
            $request->merge(['donation_frequency' => null]);
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator->messages());
        }

        $user = Auth::user();
        $token = $request->input('stripeToken');

        // Validate that at least one amount is provided
        $amount_commonchange = (float) $request->input('amount_commonchange', 0);
        $amount_group = (float) $request->input('amount_group', 0);

        if ($amount_commonchange <= 0 && $amount_group <= 0) {
            return back()->withInput()->withErrors(['amount' => 'Please enter a donation amount.']);
        }

        // Validate minimum group contribution
        if ($amount_group > 0 && $amount_group < 3) {
            return back()->withInput()->withErrors(['amount_group' => 'If you\'re making a group contribution, it must be at least $3.00.']);
        }

        if (!isset($token)) {
            return back()->withInput()->withErrors(['payment' => 'Payment token is required.']);
        }

        if (!$this->stripe) {
            return back()->withInput()->withErrors(['payment' => 'Payment processing is currently unavailable.']);
        }

        try {
            $customer = Subscription::get_or_create_customer($user, $token);
            $group = $user->default_group()->first();

            if ($request->input('donation_type') == 'recurring') {
                $period = $request->input('donation_frequency');

                if ($amount_commonchange > 0) {
                    Subscription::set_subscription($user, 'dollar_'.$period.'_cc', $amount_commonchange, StripeMetadata::create()->setCCOps()->toArray());

                    // Create donation record for Common Change recurring donation
                    Donation::create([
                        'user_id' => $user->id,
                        'group_id' => null, // Common Change donation
                        'amount' => $amount_commonchange,
                        'currency' => 'USD',
                        'type' => 'recurring',
                        'frequency' => $period,
                        'status' => 'completed',
                        'payment_method' => 'stripe',
                        'customer_id' => $customer->id,
                        'country' => 'US',
                        'processed_at' => now(),
                    ]);
                } else {
                    Subscription::cancel_subscription($user, 'dollar_'.$period.'_cc');
                }

                if ($amount_group > 0) {
                    Subscription::set_subscription($user, 'dollar_'.$period.'_group', $amount_group, StripeMetadata::create()->setGroup($group)->toArray());

                    // Create donation record for group recurring donation
                    Donation::create([
                        'user_id' => $user->id,
                        'group_id' => $group ? $group->id : null,
                        'amount' => $amount_group,
                        'currency' => 'USD',
                        'type' => 'recurring',
                        'frequency' => $period,
                        'status' => 'completed',
                        'payment_method' => 'stripe',
                        'customer_id' => $customer->id,
                        'country' => 'US',
                        'processed_at' => now(),
                    ]);
                } else {
                    Subscription::cancel_subscription($user, 'dollar_'.$period.'_group');
                }
            } else {
                // One-time charge
                if ($amount_commonchange > 0) {
                    $charge1 = $this->stripe->charges->create([
                        'amount' => $amount_commonchange * 100, // amount in cents
                        'currency' => 'usd',
                        'customer' => $customer->id,
                        'metadata' => StripeMetadata::create()->setUser($user)->setCCOps()->toArray(),
                    ]);

                    // Create donation record for Common Change one-time donation
                    Donation::create([
                        'user_id' => $user->id,
                        'group_id' => null, // Common Change donation
                        'amount' => $amount_commonchange,
                        'currency' => 'USD',
                        'type' => 'one-time',
                        'status' => 'completed',
                        'payment_method' => 'stripe',
                        'transaction_id' => $charge1->id,
                        'customer_id' => $customer->id,
                        'country' => 'US',
                        'processed_at' => now(),
                    ]);
                }

                if ($amount_group > 0) {
                    $charge2 = $this->stripe->charges->create([
                        'amount' => $amount_group * 100, // amount in cents
                        'currency' => 'usd',
                        'customer' => $customer->id,
                        'metadata' => StripeMetadata::create()->setUser($user)->setGroup($group)->toArray(),
                    ]);

                    // Create donation record for group one-time donation
                    Donation::create([
                        'user_id' => $user->id,
                        'group_id' => $group ? $group->id : null,
                        'amount' => $amount_group,
                        'currency' => 'USD',
                        'type' => 'one-time',
                        'status' => 'completed',
                        'payment_method' => 'stripe',
                        'transaction_id' => $charge2->id,
                        'customer_id' => $customer->id,
                        'country' => 'US',
                        'processed_at' => now(),
                    ]);
                }
            }

            return back()->with('success', 'Your ' . $request->input('donation_type') . ' donation was successful!');

        } catch (\Stripe\Exception\CardException $e) {
            // Create failed donation records for card declines
            if ($amount_commonchange > 0) {
                Donation::create([
                    'user_id' => $user->id,
                    'group_id' => null,
                    'amount' => $amount_commonchange,
                    'currency' => 'USD',
                    'type' => $request->input('donation_type'),
                    'frequency' => $request->input('donation_frequency'),
                    'status' => 'failed',
                    'payment_method' => 'stripe',
                    'country' => 'US',
                    'notes' => 'Card declined: ' . $e->getMessage(),
                ]);
            }

            if ($amount_group > 0) {
                Donation::create([
                    'user_id' => $user->id,
                    'group_id' => $group ? $group->id : null,
                    'amount' => $amount_group,
                    'currency' => 'USD',
                    'type' => $request->input('donation_type'),
                    'frequency' => $request->input('donation_frequency'),
                    'status' => 'failed',
                    'payment_method' => 'stripe',
                    'country' => 'US',
                    'notes' => 'Card declined: ' . $e->getMessage(),
                ]);
            }

            return back()->withInput()->withErrors(['payment' => 'Your card has been declined: ' . $e->getMessage()]);
        } catch (\Exception $e) {
            \Log::error('Donation processing error: ' . $e->getMessage());

            // Create failed donation records for other errors
            if ($amount_commonchange > 0) {
                Donation::create([
                    'user_id' => $user->id,
                    'group_id' => null,
                    'amount' => $amount_commonchange,
                    'currency' => 'USD',
                    'type' => $request->input('donation_type'),
                    'frequency' => $request->input('donation_frequency'),
                    'status' => 'failed',
                    'payment_method' => 'stripe',
                    'country' => 'US',
                    'notes' => 'Processing error: ' . $e->getMessage(),
                ]);
            }

            if ($amount_group > 0) {
                Donation::create([
                    'user_id' => $user->id,
                    'group_id' => $group ? $group->id : null,
                    'amount' => $amount_group,
                    'currency' => 'USD',
                    'type' => $request->input('donation_type'),
                    'frequency' => $request->input('donation_frequency'),
                    'status' => 'failed',
                    'payment_method' => 'stripe',
                    'country' => 'US',
                    'notes' => 'Processing error: ' . $e->getMessage(),
                ]);
            }

            return back()->withInput()->withErrors(['payment' => 'An error occurred while processing your donation. Please try again.']);
        }
    }

    /**
     * Process UK donation (placeholder for Donorbox integration)
     */
    public function processUKDonation(Request $request)
    {
        $rules = [
            'frequency' => 'required|in:one-time,monthly',
            'amount' => 'required|numeric|min:1',
            'group_id' => 'nullable|exists:groups,id',
            'gift_aid' => 'boolean',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator->messages());
        }

        $user = Auth::user();
        $amount = (float) $request->input('amount');
        $frequency = $request->input('frequency');
        $groupId = $request->input('group_id');
        $giftAid = $request->input('gift_aid', false);

        // Validate group access if specified
        if ($groupId) {
            $group = Group::find($groupId);
            if (!$group || !$group->canView($user)) {
                return back()->withInput()->withErrors(['group_id' => 'Invalid group selection.']);
            }
        }

        // For now, this is a placeholder that would integrate with Donorbox
        // In a real implementation, you would:
        // 1. Create a Donorbox donation session
        // 2. Redirect to Donorbox checkout
        // 3. Handle the webhook/callback from Donorbox

        // Store the donation intent in session for the redirect
        session([
            'uk_donation_intent' => [
                'amount' => $amount,
                'frequency' => $frequency,
                'group_id' => $groupId,
                'gift_aid' => $giftAid,
                'user_id' => $user->id,
                'timestamp' => now()
            ]
        ]);

        // For demonstration purposes, create a donation record
        // In production, this would be created after successful Donorbox payment
        Donation::create([
            'user_id' => $user->id,
            'group_id' => $groupId,
            'amount' => $amount,
            'currency' => 'GBP',
            'type' => $frequency === 'monthly' ? 'recurring' : 'one-time',
            'frequency' => $frequency === 'monthly' ? 'monthly' : null,
            'status' => 'completed', // In production, this would start as 'pending'
            'payment_method' => 'donorbox',
            'transaction_id' => 'demo_' . uniqid(), // Demo transaction ID
            'country' => 'UK',
            'notes' => $giftAid ? 'Gift Aid claimed' : null,
            'processed_at' => now(),
        ]);

        // For demonstration, we'll just show a success message
        // In production, this would redirect to Donorbox
        return back()->with('success',
            'UK donation processing initiated. Amount: £' . number_format($amount, 2) .
            ' (' . $frequency . ')' .
            ($groupId ? ' for group ID: ' . $groupId : '') .
            ($giftAid ? ' with Gift Aid' : '') .
            '. This would normally redirect to Donorbox for payment processing.'
        );
    }

    /**
     * Show the invite friends page
     */
    public function invite(Request $request)
    {
        $user = Auth::user();
        $defaultGroup = $user->default_group;

        return view('v2.accounts.invite', compact('user', 'defaultGroup'))
            ->with('old_input', $request->old());
    }

    /**
     * Display the donation history page
     */
    public function donationHistory(Request $request)
    {
        $user = Auth::user();

        // Get filter parameters
        $status = $request->get('status', 'all');
        $type = $request->get('type', 'all');
        $country = $request->get('country', 'all');
        $perPage = $request->get('per_page', 15);

        // Build query
        $query = Donation::where('user_id', $user->id)
                         ->with(['group'])
                         ->orderBy('created_at', 'desc');

        // Apply filters
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($type !== 'all') {
            $query->where('type', $type);
        }

        if ($country !== 'all') {
            $query->where('country', $country);
        }

        // Paginate results
        $donations = $query->paginate($perPage);

        // Get summary statistics
        $totalDonated = Donation::where('user_id', $user->id)
                               ->where('status', 'completed')
                               ->sum('amount');

        $totalDonations = Donation::where('user_id', $user->id)
                                 ->where('status', 'completed')
                                 ->count();

        $recurringDonations = Donation::where('user_id', $user->id)
                                     ->where('type', 'recurring')
                                     ->where('status', 'completed')
                                     ->count();

        return view('v2.finances.donation-history', compact(
            'user',
            'donations',
            'status',
            'type',
            'country',
            'perPage',
            'totalDonated',
            'totalDonations',
            'recurringDonations'
        ));
    }
}
