<?php

namespace App\Http\Controllers;

use App\Models\Address;
use App\Models\CollectionPaginator;
use App\Models\Discussion;
use App\Models\Group;
use App\Models\Issue;
use App\Models\OnesignalApiInterface;
use App\Models\PayInfo;
use App\Models\Post;
use App\Models\Proposal;
use App\Models\Recipient;
use App\Models\Sort;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Str;

class IssueController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): RedirectResponse
    {
        $group = User::get_current_group();

        return (isset($group))
            ? redirect()->route('group.issues', ['id' => $group->id])
            : redirect()->route('group.index')->withAlerts("You don't belong to any groups.");
    }

    // Create an issue in the user's default group - just redirect to the group's create issue page
    public function default_create()
    {
        $group = User::get_current_group();

        return (isset($group))
            ? redirect()->route('group.create_issue', ['id' => $group->id])
            : redirect()->route('group.index')->withAlerts("You don't belong to any groups.");
    }

    public function forGroup(Request $request, $group_id)
    {
        $user = Auth::User();
        $group = Group::find($group_id);

        // Make sure this group exists, and the user is a member.
        if (! isset($group) || ! $group->members()->find($user->id)) {
            return redirect()->route('dashboard')->withAlerts(['danger' => 'Invalid group.']);
        }

        // Make this group our default for this session:
        User::set_current_group($group);

        $issue_query = $group->issues();

        // Did they use the filter/sort form?
        $input = $request->all();
        if (! empty($input['from_date'])) {
            $issue_query = $issue_query->where('issues.created_at', '>=', Carbon::parse($input['from_date']));
        }
        if (! empty($input['to_date'])) {
            $issue_query = $issue_query->where('issues.created_at', '<=', Carbon::parse($input['to_date']));
        }
        if (! empty($input['search'])) {
            $issue_query = $issue_query->where('issues.title', 'like', '%'.$input['search'].'%');
        }

        switch ($request->input('sort', 'newest')) {
            case 'newest':
                $issue_query = $issue_query->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $issue_query = $issue_query->orderBy('created_at', 'asc');
                break;
            case 'alpha':
                $issue_query = $issue_query->orderBy('title', 'asc');
                break;
            case 'revalpha':
                $issue_query = $issue_query->orderBy('title', 'desc');
                break;
        }

        $paged_issues = $issue_query->paginate(15)->appends($request->except('page'));

        return view('v2.issues.index')->withGroup($group)->withIssues($paged_issues)->withInput($request->all());
    }

    public function admin_index(Request $request)
    {
        $issues = Issue::withTrashed()->with('proposal', 'recipient', 'discussion.group')->orderBy('updated_at', 'desc')->get();

        // Did they use the filter/sort form?
        $input = $request->all();
        if (isset($input['group'])) {
            $issues = $issues->filter(function ($issue) use ($input) {
                if ($input['group'] && ($issue->discussion->group_id != $input['group'])) {
                    return false;
                }
                if ($input['vote_status'] && ($issue->proposal->vote_status != $input['vote_status'])) {
                    return false;
                }
                if ($input['payment_status'] && ($issue->proposal->payment_status != $input['payment_status'])) {
                    return false;
                }
                if (! empty($input['search']) && (stripos($issue->title, $input['search']) === false)) {
                    return false;
                }

                return true;
            });

            switch ($input['sort']) {
                case 'recent':
                    $issues = $issues->sort(function ($a, $b) {
                        return Sort::carbon($b->updated_at, $a->updated_at);
                    });
                    break;
                case 'deadline':
                    $issues = $issues->sort(function ($a, $b) {
                        return Sort::carbon($b->proposal->deadline_c, $a->proposal->deadline_c);
                    });
                    break;
                case 'title': // alpha
                    $issues = $issues->sort(function ($a, $b) {
                        return strcasecmp($a->title, $b->title);
                    });
                    break;
            }
        } else {
            $issues = $issues->sort(function ($a, $b) {
                return
                    strcasecmp($a->discussion->group->name, $b->discussion->group->name)
                    ?: Sort::carbon($b->updated_at, $a->updated_at);
            });
        }

        $vote_status_select = [
            '0' => '-- Any Vote Status --',
            'open' => 'Open',
            'approved' => 'Approved',
            'expired' => 'Expired',
            'provisionally approved' => 'Provisionally Approved',
        ];
        $payment_status_select = [
            '0' => '-- Any Payment Status --',
            'awaiting_approval' => 'Awaiting Approval',
            'under_review' => 'Under Review',
            'need_payment_info' => 'Need Payment Info',
            'processing' => 'Processing',
            'payment_sent' => 'Payment Sent',
            'payment_received' => 'Payment Received',
            'cancelled' => 'Payment Cancelled',
        ];

        $paged_issues = CollectionPaginator::make($request, $issues, 50);

        return view('issues.admin_index')->withIssues($paged_issues)->with('vote_status_select', $vote_status_select)->with('payment_status_select', $payment_status_select)->withInput($input);
    }

    public function site_admin($issue_id)
    {
        $issue = Issue::with('proposal', 'recipient.address', 'discussion.group')->findOrFail($issue_id);

        return $issue;
    }

    private function deadline_choices($group, $issue = null)
    {
        $choices = ['' => 'Please Select'];

        // If we're editing an issue, we're using this list to extend the deadline:
        if (isset($issue)) {
            $choices['1'] = '1 day';
        }
        // GB groups want to use a shorter deadline:
        if ($group->country == 'GB') {
            $choices['2'] = '2 days';
        }
        // The rest of the choices are always used:
        $choices = array_replace(
            $choices,
            [
                '3' => '3 days',
                '5' => '5 days',
                '7' => '7 days',
                '10' => '10 days',
                '14' => '14 days']
        );

        return $choices;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($group_id)
    {
        $group = Group::find($group_id);
        if (! isset($group) || ! $group->isMember(Auth::User())) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        if (! $group->canCreateRequest(Auth::User())) {
            return redirect('home')->withAlerts(['danger' => 'Unable to create a request for this group.']);
        }

        // Give a warning if their group has insufficient funds:
        if ($group->funds_available() <= 0) {
            session()->put('alerts', ['warning' => "This group doesn't have the funds to support a new request."]);
        }

        return view(
            'v2.issues.create',
            [
                'discussion_id' => 0,
                'group' => $group,
                'deadline_choices' => Issue::deadline_choices($group),
            ]
        );
    }

    private function test_input($input)
    {
        $issue = new Issue;
        $proposal = new Proposal;
        $recipient = new Recipient;
        $address = new Address;

        $proposal->fill($input['proposal']);
        $recipient->fill($input['recipient']);
        $address->fill($input['recipient']['address'] ?? []);

        $errors = new MessageBag;
        if (! $issue->isValid($input)) {
            $errors->merge($issue->errors);
        }
        if (! $proposal->isValid()) {
            $errors->merge($proposal->errors);
        }
        if (! $recipient->isValid()) {
            $errors->merge($recipient->errors);
        }
        // if (! $address->isValid()) {
        //     $errors->merge($address->errors);
        // }

        return $errors;
    }

    private function test_pay_info($input)
    {
        $pay_info = new PayInfo;
        $address = new Address;

        $pay_info->fill($input['proposal']['pay_info'] ?? []);
        // $address->fill($input['proposal']['pay_info']['address']);

        $errors = new MessageBag;
        if (! $pay_info->isValid()) {
            $errors->merge($pay_info->errors);
        }
        // if (!$address->isValid())     { $errors->merge($address->errors); }

        return $errors;
    }

    private function test_input_for_update($input, $proposal)
    {
        $issue = new Issue;
        $recipient = new Recipient;
        $address = new Address;

        $proposal->fill($input['proposal']);
        $recipient->fill($input['recipient']);
        $address->fill($input['recipient']['address'] ?? []);

        $errors = new MessageBag;
        if (! $issue->isValid($input)) {
            $errors->merge($issue->errors);
        }
        if (! $proposal->isValid()) {
            $errors->merge($proposal->errors);
        }
        if (! $recipient->isValid()) {
            $errors->merge($recipient->errors);
        }
        // if (! $address->isValid()) {
        //     $errors->merge($address->errors);
        // }

        return $errors;
    }

    public function confirm(Request $request)
    {
        $discussion_id = $request->input('discussion_id', 0);
        $group = ($discussion_id) ? Discussion::findOrFail($discussion_id)->group()->first() : Group::find($request->input('group_id'));
        if (! (isset($group) && $group->canCreateRequest(Auth::User()))) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        // How much will the group's fund allow for?
        Proposal::set_max_amount($group->funds_available()); // this is used for the validation below
        $errors = $this->test_input($request->all());
        $errors_p = $this->test_pay_info($request->all());
        if ($errors->count() || $errors_p->count()) {
            return back()->withInput()->withErrors($errors)->with('errors_p', $errors_p)->withAlerts(['danger' => 'Form Errors: Scroll down for details']);
        }

        // I hate that i have to do this, but selectBoxIt doesn't set the select box name unless a value is chosen....arg.
        $input = $request->all();
        $input['recipient']['addr1'] = $input['recipient']['addr1'] ?? '';
        $input['recipient']['addr2'] = $input['recipient']['addr2'] ?? '';
        $input['recipient']['city'] = $input['recipient']['city'] ?? '';
        $input['recipient']['state'] = $input['recipient']['state'] ?? '';
        $input['recipient']['postcode'] = $input['recipient']['postcode'] ?? '';
        $input['recipient']['country'] = $input['recipient']['country'] ?? '';
        $input['recipient']['phone'] = $input['recipient']['phone'] ?? '';
        $input['recipient']['email'] = $input['recipient']['email'] ?? '';
        if (isset($input['recipient']['address'])) {
            $input['recipient']['address']['addr1'] = $input['recipient']['address']['addr1'] ?? '';
            $input['recipient']['address']['addr2'] = $input['recipient']['address']['addr2'] ?? '';
            $input['recipient']['address']['city'] = $input['recipient']['address']['city'] ?? '';
            $input['recipient']['address']['state'] = $input['recipient']['address']['state'] ?? '';
            $input['recipient']['address']['postcode'] = $input['recipient']['address']['postcode'] ?? '';
            $input['recipient']['address']['country'] = $input['recipient']['address']['country'] ?? '';
        }
        $input['proposal']['pay_info']['address']['state'] =
            isset($input['proposal']['pay_info']['address']['state'])
            ? $input['proposal']['pay_info']['address']['state'] : '';
        $input['proposal']['pay_info']['address']['country'] =
            isset($input['proposal']['pay_info']['address']['country'])
            ? $input['proposal']['pay_info']['address']['country'] : '';
        $input['proposal']['pay_info']['payment_type'] =
            isset($input['proposal']['pay_info']['payment_type'])
            ? $input['proposal']['pay_info']['payment_type'] : '';

        return view('v2.issues.confirm')->with('input', $input)->with('group', $group);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::User();
        $discussion_id = $request->input('discussion_id', 0);
        $group = ($discussion_id) ? Discussion::findOrFail($discussion_id)->group()->first() : Group::find($request->input('group_id'));
        if (! (isset($group) && $group->canCreateRequest(Auth::User()))) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        // First, test the input

        // How much will the group's fund allow for?
        Proposal::set_max_amount($group->funds_available()); // this is used for the validation below
        $errors = $this->test_input($request->all());
        $errors_p = $this->test_pay_info($request->all());
        if ($errors->count() || $errors_p->count()) {
            return back()->withInput()->withErrors($errors)->withErrorsP($errors_p);
        }

        // If this isn't being attached to a discussion, create a new discussion and assign it to their default group
        if ($request->input('discussion_id')) {
            $discussion = Discussion::findOrFail($request->input('discussion_id'));
            if ($discussion->hasIssue()) {
                // This discussion already has an issue.  Don't let them create a new one. Show them the existing issue.
                return redirect()->route('issue.show', ['issue' => $discussion->issue->id]);
            }
        } else {
            $discussion = new Discussion;
            $discussion->title = $request->input('title');
            $discussion->group()->associate($group);
            $discussion->creator()->associate($user);
            $discussion->save();
            $discussion->post_comment($user, 'Issue created');
        }

        $issue = new Issue;
        $proposal = new Proposal;
        $recipient = new Recipient;
        $address = new Address;
        $pay_info = new PayInfo;
        $pay_address = new Address;

        $issue->fill($request->all());
        $proposal->fill($request->input('proposal'));
        $recipient->fill($request->input('recipient'));
        $address->fill($request->input('recipient.address', []));
        $pay_info->fill($request->input('proposal.pay_info', []));
        $pay_address->fill($request->input('proposal.pay_info.address', []));

        $recipient->save();
        $recipient->address()->save($address);

        $pay_info->save();
        $pay_info->address()->save($pay_address);

        $issue->creator()->associate($user);
        $issue->discussion()->associate($discussion);
        $issue->recipient()->associate($recipient);
        $issue->save();
        $proposal->issue()->associate($issue);
        $proposal->creator()->associate($user);
        $proposal->pay_info()->associate($pay_info);
        $proposal->save();
        $issue->mark_read($user);
        $discussion->mark_read($user);
        $proposal->vote($user, 'concur', false);

        // Send notifications
        $issue_id = $issue->id;
        dispatch(function () use ($issue_id) {

            // First, send to group members that have per_comment notification preference:
            $issue = Issue::with('discussion.group.active_members', 'creator')->find($issue_id);
            $group = $issue->discussion->group;

            if (! $group->is_network) {
                $template_data = [
                    'issue' => $issue,
                ];
                $subject = $group->name.': '.$issue->title;
                $email_per_comment_members = $group->active_members->filter(function ($user) {
                    return $user->pivot->email_pref == 'per_comment';
                });
                foreach ($email_per_comment_members as $member) {
                    Mail::send(['emails.new_issue', 'emails.new_issue-text'], $template_data, function ($message) use ($member, $subject) {
                        $message->to($member->email, $member->firstname.' '.$member->lastname);
                        $message->subject($subject);
                    });
                }
            }

            // Also send notification to admin email:
            Mail::send(['emails.new_issue', 'emails.new_issue-text'], ['issue' => $issue], function ($message) use ($issue) {
                $message->to('<EMAIL>');
                $message->subject('New request for '.$issue->discussion->group->name);
            });

            // Finally, send app notification to group members that have a mobile app associated w/ their account:
            $onesignal_ids = $group->getOneSignalIDs();
            if (count($onesignal_ids)) {
                $subject = 'New request in '.$group->name;
                $message = $issue->title;
                $url = URL::route('issue.show', ['issue' => $issue->id]);
                $mobile = new OnesignalApiInterface;
                $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
            }
        })->afterResponse();

        return redirect()->route('issue.index')->withAlerts(['success' => 'Request created!']);

    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        $user = Auth::User();
        $issue = Issue::withTrashed()->with('discussion.group', 'users_read')->find($id);

        if (! isset($issue) || ! $issue->canView($user)) {
            abort(404);
        }

        $issue->mark_read($user);
        $issue->discussion->mark_read($user);

        $proposal = $issue->proposal;

        $current_votes = $proposal->votes_for_chart();
        $vote_tally = $proposal->vote_tally();

        // Assemble a list of users (firstname, lastname, email) who haven't voted yet.
        $voting_members = $issue->discussion->group->active_members()->get();
        $voted_members = $proposal->users_voted()->get();
        $missing_voters = $voting_members->diff($voted_members);

        $missing_voter_info = $missing_voters->reduce(function ($result, $user) {
            $result[] = "$user->firstname $user->lastname <$user->email>";
            return $result;
        }, []);

        $missing_voter_info = implode(', ', $missing_voter_info);

        return view('v2.issues.show')
            ->withIssue($issue)
            ->withProposal($proposal)
            ->with('currentVotes', $current_votes)
            ->withTally($vote_tally)
            ->withMissingVoters($missing_voter_info);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id): Response
    {
        $issue = Issue::with('recipient.address', 'proposal.pay_info.address')->findOrFail($id);

        if (! $issue->canEdit(Auth::User())) {
            return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['warning' => "You don't have permission to edit this issue"]);
        }

        return view(
            'issues.edit',
            [
                'issue' => $issue,
                'deadline_choices' => Issue::deadline_choices($issue->discussion->group, true),
            ]
        );
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id): Response
    {
        $issue = Issue::find($id);

        if (! isset($issue)) {
            return 'Issue not found';
        }

        if (! $issue->canEdit(Auth::User())) {
            return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['warning' => "You don't have permission to edit this issue"]);
        }

        // How much will the group's fund allow for?
        $max_amount = $issue->discussion()->first()->group()->first()->funds_available() + $issue->proposal()->first()->amount;
        Proposal::set_max_amount($max_amount);

        $proposal = $issue->proposal;
        $errors = $this->test_input_for_update($request->all(), $proposal);
        $errors_p = $this->test_pay_info($request->all());
        if ($errors->count() || $errors_p->count()) {
            return back()->withInput()->withErrors($errors)->withErrorsP($errors_p)->withAlerts(['danger' => 'Form Errors: Scroll down for details']);
        }

        if (is_null($issue->recipient)) {
            $recipient = new Recipient;
            $address = new Address;
            $recipient->save();
            $issue->recipient()->associate($recipient);
            $address->addressable()->associate($recipient);
            $address->fill($request->input('recipient.address', []));
            $address->save();
        } else {
            $recipient = $issue->recipient;
            $address = $recipient->address;
            $address->fill($request->input('recipient.address', []));
        }
        $pay_info = $proposal->pay_info()->first();

        $issue->update($request->all());
        // $proposal->fill($request->input('proposal')); // we already did this in $this->test_input_for_update() above

        // take care of the case where we extend a proposal's deadline after it's expired:
        if (strtotime($proposal->deadline) > time()) {
            $proposal->voting_closed = 0;
            $proposal->payment_status = 'awaiting_approval';
        }
        $proposal->save();

        $recipient->update($request->input('recipient'));

        $address->update($request->input('recipient.address', []));

        if (! isset($pay_info)) {
            $pay_info = PayInfo::create($request->input('proposal.pay_info', []));
            $pay_address = new Address;
            $pay_address->fill($request->input('proposal.pay_info.address', []));
            $pay_info->address()->save($pay_address);
            $proposal->pay_info()->associate($pay_info);
            $proposal->save();
        } else {
            $pay_address = $pay_info->address()->first();
            $pay_info->update($request->input('proposal.pay_info', []));
            $pay_address->update($request->input('proposal.pay_info.address', []));
        }

        return redirect()->route('issue.edit', ['issue' => $id])->withAlerts(['success' => 'Issue Updated']);

    }

    public function admin_update(Request $request, $id)
    {
        $issue = Issue::find($id);
        if (! isset($issue)) {
            return 'Issue not found';
        }

        $user = Auth::User();
        if (! $user->is_admin) {
            return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['warning' => "You don't have permission to edit this issue"]);
        }

        $new_payment_status = $request->input('payment_status');
        if ($new_payment_status == 'awaiting_approval') {
            return back()->withAlerts(['warning' => "Cannot manually set status to 'Awaiting Approval'."]);
        }

        $proposal = $issue->proposal()->first();
        $proposal->payment_status = $new_payment_status;
        $proposal->save();

        /* Need to talk w/ Valerie about this one:
        if ($proposal->payment_status == 'payment_sent') {
            $discussion = $issue->discussion()->first();
            $post = new Post(array('comment'=>'Payment has been sent!'));
            $post->author()->associate($user);
            $discussion->posts()->save($post);
            $post->mark_read($user);
        }
        */

        return back()->withAlerts(['success' => 'Status updated.']);
    }

    /**
     * Performs a soft delete on a given issue
     */
    public function admin_delete(int $id): Response
    {
        $issue = Issue::find($id);
        if (! isset($issue)) {
            return 'Issue not found';
        }

        $user = Auth::User();
        if (! $user->is_admin) {
            return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['warning' => "You don't have permission to edit this issue"]);
        }

        $proposal = $issue->proposal()->first();
        $proposal->timestamps = false;
        $proposal->delete();
        $discussion = $issue->discussion()->first();
        $discussion->timestamps = false;
        $discussion->delete();
        $issue->timestamps = false;
        $issue->delete();

        return back()->withAlerts(['success' => 'Request deleted.']);
    }

    /**
     * Undeletes a soft-deleted issue
     */
    public function admin_restore(int $id): Response
    {
        $issue = Issue::withTrashed()->find($id);
        if (! isset($issue)) {
            return 'Issue not found';
        }

        $user = Auth::User();
        if (! $user->is_admin) {
            return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['warning' => "You don't have permission to edit this issue"]);
        }

        if ($issue->trashed()) {
            $proposal = $issue->proposal()->first();
            $proposal->timestamps = false;
            $proposal->restore();
            $discussion = $issue->discussion()->first();
            $discussion->timestamps = false;
            $discussion->restore();
            $issue->timestamps = false;
            $issue->restore();
        }

        return back()->withAlerts(['success' => 'Request restored.']);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): Response
    {
        //
    }

    public function post(Request $request, $issue_id)
    {
        $issue = Issue::findOrFail($issue_id);
        $discussion = $issue->discussion;
        $me = Auth::User();
        $my_id = $me->id;

        if (! $discussion->canComment($me)) {
            return back()->withAlerts(['danger' => 'Invalid action.']);
        }

        $post = $discussion->post_comment($me, $request->input('comment'));

        $post_id = $post->id;
        dispatch(function () use ($issue_id, $post_id, $my_id) {
            // Send email notification to group members that have per_comment notification preference:
            $post = Post::find($post_id);
            $issue = Issue::with('discussion.group.active_members', 'creator.mobile_apps_notify')->find($issue_id);
            $me = User::find($my_id);

            $group = $issue->discussion->group;
            if (! $group->is_network) {
                $template_data = [
                    'issue' => $issue,
                    'post' => $post,
                ];
                $subject = 'RE: '.$group->name.': '.$issue->title;
                $email_per_comment_members = $group->active_members->filter(function ($user) {
                    return $user->pivot->email_pref == 'per_comment';
                });
                foreach ($email_per_comment_members as $member) {
                    Mail::send(['emails.comment', 'emails.comment-text'], $template_data, function ($message) use ($member, $subject) {
                        $message->to($member->email, $member->firstname.' '.$member->lastname);
                        $message->subject($subject);
                    });
                }
            }

            // Finally, send app notification to group members that have a mobile app associated w/ their account:
            if (count($issue->creator->mobile_apps_notify)) {
                $onesignal_ids = [];
                foreach ($issue->creator->mobile_apps_notify as $mobile_app) {
                    $onesignal_ids[] = $mobile_app->one_signal_id;
                }
                $subject = "New comment on '".$issue->title."'";
                $message = $me->firstname.' '.$me->lastname.' says: '.Str::limit($post->niceComment, $limit = 150, '...');
                $url = URL::route('issue.show', ['issue' => $issue->id]);
                $mobile = new OnesignalApiInterface;
                $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
            }
        })->afterResponse();

        return back();
    }
}
