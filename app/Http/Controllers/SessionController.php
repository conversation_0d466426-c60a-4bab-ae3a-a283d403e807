<?php

namespace App\Http\Controllers;

use App\Models\GroupInvite;
use App\Models\User;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;

class SessionController extends Controller
{
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function create()
    {
        try {
            // Log the login page access for debugging
            \Log::info('Login page accessed via controller', [
                'authenticated' => Auth::check(),
                'user_id' => Auth::check() ? Auth::id() : null,
                'route' => request()->route()->getName(),
                'url' => request()->url(),
                'path' => request()->path(),
                'method' => request()->method(),
                'headers' => request()->headers->all(),
                'session_id' => session()->getId()
            ]);

            // If user is already authenticated, redirect to dashboard
            if (Auth::check()) {
                return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
            }

            // Get email from session if available (for group invites)
            $email = session()->pull('group_invite_email', '');

            // Return the login view with debug information
            return view('auth.login', [
                'email' => $email,
                'route' => request()->route()->getName(),
                'debug_info' => [
                    'url' => request()->url(),
                    'path' => request()->path(),
                    'method' => request()->method(),
                    'route_name' => request()->route()->getName(),
                    'session_id' => session()->getId(),
                    'headers' => request()->headers->all()
                ]
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error in login page', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a friendly error page
            return response()->view('errors.500', [
                'message' => 'An error occurred while loading the login page. Please try again.'
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        // Add rate limiting
        if (RateLimiter::tooManyAttempts('login:'.$request->ip(), 5)) {
            $seconds = RateLimiter::availableIn('login:'.$request->ip());
            return back()->withInput()->withErrors([
                'invalid' => "Too many login attempts. Please try again in {$seconds} seconds."
            ]);
        }

        $remember = ($request->has('remember'));

        // Log the login attempt for debugging
        \Log::info('Login attempt', [
            'email' => $request->input('email'),
            'remember' => $remember,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        if (Auth::attempt($request->only('email', 'password'), $remember)) {
            // Clear rate limiter on successful login
            RateLimiter::clear('login:'.$request->ip());

            // Regenerate the session to prevent session fixation
            $request->session()->regenerate();

            $user = Auth::User();

            // Log authentication status after login
            \Log::info('Authentication status after login', [
                'user_id' => $user->id,
                'authenticated' => Auth::check(),
                'session_id' => session()->getId()
            ]);

            User::set_current_group($user->default_group()->first());

            // check and see if someone's sent an invite to an email address that isn't on this user's account
            GroupInvite::checkToken();

            if ($remember) {
                session()->put('set_remember_expir', true);
            }

            // Store a flag in the session to indicate successful login
            session()->put('auth_checked', true);
            session()->put('user_id', $user->id);

            // Force the session to be saved immediately
            session()->save();

            // Always redirect to the user's dashboard
            $intended = route('dashboard.new', ['user_id' => $user->id]);

            // Log the redirection for debugging
            \Log::info('Redirecting after successful login', [
                'intended_url' => $intended,
                'user_id' => $user->id,
                'session_id' => session()->getId()
            ]);

            // Create a response with the redirect
            $response = redirect($intended);

            // Set a secure cookie with improved settings
            $isSecure = $request->secure() || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
            $response->cookie(
                'auth_user_id',
                $user->id,
                60, // 60 minutes
                '/',
                null,
                $isSecure,
                true, // httpOnly
                false,
                'Lax' // SameSite
            );

            return $response;
        }

        // Increment rate limiter on failed attempt
        RateLimiter::hit('login:'.$request->ip());

        // Log failed login attempt
        \Log::warning('Failed login attempt', [
            'email' => $request->input('email'),
            'ip' => $request->ip()
        ]);

        return back()->withInput()->withErrors(['invalid' => 'You\'ve entered an incorrect password or email address.']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     */
    public function destroy(): RedirectResponse
    {
        // Log before logout
        \Log::info('Logout method called', [
            'authenticated' => Auth::check(),
            'user_id' => Auth::check() ? Auth::id() : null,
            'session_id' => session()->getId()
        ]);

        // Get the current session ID for logging
        $oldSessionId = session()->getId();

        // Perform logout
        Auth::logout();

        // Flush the session
        session()->flush();

        // Regenerate the session ID
        session()->regenerate(true);

        // Clear any cookies
        $response = redirect()->route('auth.login')->with('success', 'You have been logged out successfully.');

        // Remove the auth_user_id cookie
        $response->cookie('auth_user_id', '', -1);

        // Log after logout
        \Log::info('After logout', [
            'old_session_id' => $oldSessionId,
            'new_session_id' => session()->getId(),
            'authenticated' => Auth::check()
        ]);

        return $response;
    }

    public function impersonate($user_id)
    {
        $impersonator = Auth::User();
        if (! $impersonator->can_impersonate) {
            abort(404);
        }

        if ((int) $user_id == $impersonator->id) {
            return back()->withAlerts(['warning' => "You're trying to impersonate yourself"]);
        }

        $user = User::find($user_id);

        if (! isset($user)) {
            return redirect()->route('dashboard')->withAlerts(['danger' => 'User not found']);
        }

        $admin_id = Auth::User()->id;
        session()->flush();
        Auth::login($user);
        User::set_current_group(Auth::User()->default_group()->first());
        session()->put('impersonater_id', $admin_id);

        return redirect()->route('dashboard')->withAlerts(['info' => 'Impersonating user']);
    }

    public function stop_impersonating(): RedirectResponse
    {
        $impersonator = User::findOrFail(session()->get('impersonater_id', 0));
        if ($impersonator->is_admin) {
            session()->flush();
            Auth::login($impersonator);
            User::set_current_group(Auth::User()->default_group()->first());
        }

        return redirect()->route('dashboard')->withAlerts(['info' => 'You are no longer impersonating']);
    }

    /**
     * Redirect the user to the Google authentication page.
     */
    public function redirectToGoogle(): RedirectResponse
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle the callback from Google OAuth.
     */
    public function handleGoogleCallback(): RedirectResponse
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Log successful OAuth response
            \Log::info('Google OAuth successful', [
                'email' => $googleUser->getEmail(),
                'id' => $googleUser->getId(),
            ]);

            // Check if user exists with this email
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                // User exists, log them in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for pending invites
                GroupInvite::checkToken();

                return redirect()->route('dashboard.new', ['user_id' => $user->id]);
            } else {
                // User doesn't exist, create a new user automatically
                // Extract first and last name
                $nameParts = explode(' ', $googleUser->getName(), 2);
                $firstName = $nameParts[0] ?? '';
                $lastName = $nameParts[1] ?? '';

                // Create a new user
                $user = new User;
                $user->firstname = $firstName;
                $user->lastname = $lastName;
                $user->email = $googleUser->getEmail();
                // Generate a random password (user can reset it later if needed)
                $user->password = bcrypt(Str::random(16));
                $user->email_valid = true;
                $user->email_verified_at = now();
                $user->save();

                // Make user a member of the Common Change Network (group ID 1)
                $user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]);

                // Log the user in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for group invites
                GroupInvite::checkTokenAndEmail();

                return redirect()->route('dashboard.new', ['user_id' => $user->id])->withAlerts(['success' => 'Your account has been created successfully!']);
            }
        } catch (Exception $e) {
            // Log the detailed error
            \Log::error('Google OAuth failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('auth.login')
                ->withAlerts(['danger' => 'Google login failed: '.$e->getMessage()]);
        }
    }

    public function redirectToMicrosoft()
    {
        return Socialite::driver('microsoft')
            ->scopes(['openid', 'profile', 'email', 'offline_access', 'User.Read'])
            ->with([
                'prompt' => 'select_account',
            ])
            ->redirect();
    }

    public function handleMicrosoftCallback(): RedirectResponse
    {
        try {
            $microsoftUser = Socialite::driver('microsoft')->user();

            // Log successful OAuth response
            \Log::info('Microsoft OAuth successful', [
                'email' => $microsoftUser->getEmail(),
                'id' => $microsoftUser->getId(),
            ]);

            // Check if user exists with this email
            $user = User::where('email', $microsoftUser->getEmail())->first();

            if ($user) {
                // User exists, log them in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for pending invites
                GroupInvite::checkToken();

                return redirect()->route('dashboard.new', ['user_id' => $user->id]);
            } else {
                // User doesn't exist, create a new user automatically
                // Extract first and last name
                $nameParts = explode(' ', $microsoftUser->getName(), 2);
                $firstName = $nameParts[0] ?? '';
                $lastName = $nameParts[1] ?? '';

                // Create a new user
                $user = new User;
                $user->firstname = $firstName;
                $user->lastname = $lastName;
                $user->email = $microsoftUser->getEmail();
                // Generate a random password (user can reset it later if needed)
                $user->password = bcrypt(Str::random(16));
                $user->email_valid = true;
                $user->email_verified_at = now();
                $user->microsoft_id = $microsoftUser->getId();
                $user->save();

                // Make user a member of the Common Change Network (group ID 1)
                $user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]);

                // Log the user in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for group invites
                GroupInvite::checkTokenAndEmail();

                return redirect()->route('dashboard.new', ['user_id' => $user->id])->withAlerts(['success' => 'Your account has been created successfully!']);
            }
        } catch (Exception $e) {
            // Log the detailed error
            \Log::error('Microsoft OAuth failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('auth.login')
                ->withAlerts(['danger' => 'Microsoft login failed: '.$e->getMessage()]);
        }
    }

    /**
     * Redirect the user to the Facebook authentication page.
     */
    public function redirectToFacebook(): RedirectResponse
    {
        return Socialite::driver('facebook')->redirect();
    }

    /**
     * Handle the callback from Facebook OAuth.
     */
    public function handleFacebookCallback(): RedirectResponse
    {
        try {
            $facebookUser = Socialite::driver('facebook')->user();

            // Log successful OAuth response
            \Log::info('Facebook OAuth successful', [
                'email' => $facebookUser->getEmail(),
                'id' => $facebookUser->getId(),
            ]);

            // Check if user exists with this email
            $user = User::where('email', $facebookUser->getEmail())->first();

            if ($user) {
                // User exists, log them in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for pending invites
                GroupInvite::checkToken();

                return redirect()->route('dashboard.new', ['user_id' => $user->id]);
            } else {
                // User doesn't exist, create a new user automatically
                // Extract first and last name
                $nameParts = explode(' ', $facebookUser->getName(), 2);
                $firstName = $nameParts[0] ?? '';
                $lastName = $nameParts[1] ?? '';

                // Create a new user
                $user = new User;
                $user->firstname = $firstName;
                $user->lastname = $lastName;
                $user->email = $facebookUser->getEmail();
                // Generate a random password (user can reset it later if needed)
                $user->password = bcrypt(Str::random(16));
                $user->email_valid = true;
                $user->email_verified_at = now();
                $user->facebook_id = $facebookUser->getId();
                $user->save();

                // Make user a member of the Common Change Network (group ID 1)
                $user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]);

                // Log the user in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for group invites
                GroupInvite::checkTokenAndEmail();

                return redirect()->route('dashboard.new', ['user_id' => $user->id])->withAlerts(['success' => 'Your account has been created successfully!']);
            }
        } catch (Exception $e) {
            // Log the detailed error
            \Log::error('Facebook OAuth failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('auth.login')
                ->withAlerts(['danger' => 'Facebook login failed: '.$e->getMessage()]);
        }
    }

    /**
     * Redirect the user to the Apple authentication page.
     */
    public function redirectToApple(): RedirectResponse
    {
        return Socialite::driver('apple')->redirect();
    }

    /**
     * Handle the callback from Apple OAuth.
     */
    public function handleAppleCallback(): RedirectResponse
    {
        try {
            $appleUser = Socialite::driver('apple')->user();

            // Log successful OAuth response
            \Log::info('Apple OAuth successful', [
                'email' => $appleUser->getEmail(),
                'id' => $appleUser->getId(),
            ]);

            // Check if user exists with this email
            $user = User::where('email', $appleUser->getEmail())->first();

            if ($user) {
                // User exists, log them in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for pending invites
                GroupInvite::checkToken();

                return redirect()->route('dashboard.new', ['user_id' => $user->id]);
            } else {
                // User doesn't exist, create a new user automatically
                // Extract first and last name from email if name is not provided
                $name = $appleUser->getName() ?? explode('@', $appleUser->getEmail())[0];
                $nameParts = explode(' ', $name, 2);
                $firstName = $nameParts[0] ?? '';
                $lastName = $nameParts[1] ?? '';

                // Create a new user
                $user = new User;
                $user->firstname = $firstName;
                $user->lastname = $lastName;
                $user->email = $appleUser->getEmail();
                // Generate a random password (user can reset it later if needed)
                $user->password = bcrypt(Str::random(16));
                $user->email_valid = true;
                $user->email_verified_at = now();
                $user->apple_id = $appleUser->getId();
                $user->save();

                // Make user a member of the Common Change Network (group ID 1)
                $user->groups()->attach(1, ['role' => 'member', 'is_absent' => true]);

                // Log the user in
                Auth::login($user);
                User::set_current_group($user->default_group()->first());

                // Check for group invites
                GroupInvite::checkTokenAndEmail();

                return redirect()->route('dashboard.new', ['user_id' => $user->id])->withAlerts(['success' => 'Your account has been created successfully!']);
            }
        } catch (Exception $e) {
            // Log the detailed error
            \Log::error('Apple OAuth failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('auth.login')
                ->withAlerts(['danger' => 'Apple login failed: '.$e->getMessage()]);
        }
    }
}
