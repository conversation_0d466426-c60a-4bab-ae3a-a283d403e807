<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Group extends Model
{
    use SoftDeletes;

    public $num_new_discussions = 0;

    public $num_new_issues = 0;

    private $is_invited = [];

    private $user_can_invite = [];

    private static $invite_matrix = [  // $invite_matrix[$group->inviter][$user->pivot->role]
        'owner' => ['owner' => true, 'admin' => false, 'member' => false],
        'admin' => ['owner' => true, 'admin' => true, 'member' => false],
        'member' => ['owner' => true, 'admin' => true, 'member' => true],
    ];

    protected $appends = ['can_join'];

    protected $fillable = ['name', 'shortname', 'description', 'covenant',
        'url',
        'city', 'state', 'postcode', 'country', 'profile_pic', 'type', 'inviter',
        'min_donation', 'donation_frequency', 'group_banner_image'];

    protected $attributes = [
        'postcode' => '',
    ];

    // DEFINE SOME RELATIONSHIPS:
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->withPivot('role', 'is_absent', 'email_notification')
            ->using(GroupUserPivot::class);
    }

    public function active_members()
    {
        return $this->members()->isActive()->wherePivot('is_absent', '=', false);
    }

    public function mobile_notification_members()
    {
        return $this->active_members()->has('mobile_apps_notify');
    }

    public function discussions(): HasMany
    {
        return $this->hasMany(Discussion::class)->nonIssue();
    }

    public function unread_discussions(): HasMany
    {
        return $this->hasMany(Discussion::class)->nonIssue()->hasUnreadPosts();
    }

    /* public function issues(): HasManyThrough     { return $this->morphMany(Issue::class, 'issueable')->orderBy('created_at','DESC'); } */
    public function issues(): HasManyThrough
    {
        return $this->hasManyThrough(Issue::class, Discussion::class);
    }

    public function new_issues(): HasManyThrough
    {
        return $this->hasManyThrough(Issue::class, Discussion::class)->unread();
    }

    public function discussions_and_issues(): HasMany
    {
        return $this->hasMany(Discussion::class);
    }

    public function recipients(): HasManyThrough
    {
        return $this->hasManyThrough(Recipient::class, Issue::class);
    }

    public function voting_matrix(): BelongsTo
    {
        return $this->belongsTo(VotingMatrix::class);
    }

    public function voting_matrix_rows(): HasManyThrough
    {
        return $this->hasManyThrough(VotingMatrixRow::class, VotingMatrix::class);
    }

    public function group_invites(): HasMany
    {
        return $this->hasMany(GroupInvite::class);
    }

    public function join_requests(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'join_requests')
            ->withPivot('message')
            ->withTimestamps();
    }

    public function donations(): HasMany
    {
        return $this->hasMany(Donation::class);
    }

    public function scopeNotSuspended($q)
    {
        return $q->where('suspended', false);
    }

    public function scopeNotSecret($q)
    {
        return $q->where('type', 'not like', 'secret');
    }

    public function scopeHasMember($q, $user)
    {
        return $q->whereHas('members', function (Builder $query) use ($user) {
            return $query->where('users.id', $user->id);
        });
    }

    public function scopeHasNotInvited($q, $user)
    {
        return $q->whereDoesntHave('group_invites', function (Builder $query) use ($user) {
            return $query->where('user_id', $user->id);
        });
    }

    public function scopeHasInvited($q, $user)
    {
        return $q->whereHas('group_invites', function (Builder $query) use ($user) {
            return $query->where('user_id', $user->id);
        });
    }

    public function setShortnameAttribute($value)
    {
        $this->attributes['shortname'] = empty($value) ? null : $value;
    }

    public function getCurrencyAttribute()
    {
        switch ($this->country) {
            case 'ZA': return 'R';
            case 'GB': return '£';
            default: return '$';
        }
    }

    // Currently we only have the Common Change network:
    public function getIsNetworkAttribute()
    {
        return $this->id == 1;
    }

    public function owner()
    {
        return $this->members->filter(function ($member) {
            return $member->pivot->role == 'owner';
        })->first();
    }

    // How much money has been voted to be shared (includes requests not yet paid)
    /* This is currently obolete, we should refer to $group->shared_funds db field.
    public function shared() {
        $this->load('issues.proposal');

        $total_shared = 0;
        foreach ($this->issues as $issue) {
            if ($issue->proposal->status == 'approved') {
                $total_shared += $issue->proposal->amount;
            }
        }
        return sprintf("%01.2f", $total_shared);
    }
    */

    // How much money is being asked for in requests that have yet to be paid or voted down?
    public function funds_pending()
    {
        $this->load('issues.proposal');
        $open_issues = $this->issues->filter(function ($issue) {
            if (! ($issue->proposal->voting_closed)) {
                return true;
            } elseif (! $issue->proposal->vote_passed) {
                return false;
            } else {
                switch ($issue->proposal->payment_status) {
                    case 'payment_sent':
                    case 'payment_received':
                    case 'cancelled':
                        return false;
                    default:
                        return true;
                }
            }
        });

        // tally up the total of unpaid/pending requests
        $amount = 0;
        foreach ($open_issues as $issue) {
            $amount += $issue->proposal->amount;
        }

        return $amount;
    }

    // Used to determine if group has sufficient funds to fund a new request
    public function funds_available()
    {
        return $this->current_funds - $this->funds_pending();
    }

    public function isMember($user)
    {
        return ! $this->suspended && $this->members()->find($user->id);
    }

    public function isInvited($user)
    {
        if ($this->suspended) {
            return false;
        }
        $is_invited = &$this->is_invited;
        $id = $user->id;
        if (! isset($is_invited[$id])) {
            $is_invited[$id] = (GroupInvite::where('group_id', '=', $this->id)->where('user_id', '=', $id)->count());
        }

        return $is_invited[$id];
    }

    public function canView($user)
    {
        return ! $this->suspended && (($this->type != 'secret') || $this->isMember($user) || $this->isInvited($user)) || $user->is_admin;
    }

    public function canCreateRequest($user)
    {
        return ! $this->suspended && $this->active_members()->find($user->id);
    }

    public function getCanJoinAttribute()
    {
        $user = Auth::User();

        return (isset($user)) ? $this->canJoin($user) : null;
    }

    public function canJoin($user)
    {
        return ! $this->suspended && (($this->type == 'public') || $this->isInvited($user));
    }

    public function isPublicViewable()
    {
        return ! ($this->suspended || ($this->type == 'secret'));
    }

    public function canInvite($user)
    {
        if ($this->suspended) {
            return false;
        }
        $user_can_invite = &$this->user_can_invite;
        $id = $user->id;
        $member = $group->members->contains($user);
        if (! isset($user_can_invite[$id])) {
            $user_can_invite[$id] = (isset($member) && static::$invite_matrix[$group->inviter][$member->pivot->role]);
        }

        return $user_can_invite[$id];
    }

    public function canAdmin($user)
    {
        if ($user->is_admin) {
            return true;
        }
        if ($this->suspended) {
            return false;
        }

        $member = $this->members->find($user);

        return isset($member) && ($member->pivot->role == 'admin' || $member->pivot->role == 'owner');
    }

    public function canEdit($user)
    {
        if ($user->is_admin) {
            return true;
        }
        if ($this->suspended) {
            return false;
        }

        $member = $this->members->find($user);

        return isset($member) && ($member->pivot->role == 'admin' || $member->pivot->role == 'owner');
    }

    public function canUpdateEmailPref($user)
    {
        if ($user->is_admin) {
            return true;
        }
        if ($this->suspended) {
            return false;
        }

        $member = $this->members->find($user);

        return isset($member);
    }

    public function addMember($user)
    {
        if (! ($this->members()->find($user->id))) {
            $this->members()->attach($user);
            if ($user->default_group_id == 1) {
                $user->default_group_id = $this->id;
                $user->save();
            }
        }

        // delete any invites this user may have for this group:
        GroupInvite::removeUser($user, $this);
    }

    public function pending_requests()
    {
        return $this->issues->filter(function ($issue) {
            return ! $issue->proposal->voting_closed;
        });
    }

    public $errors;

    public static $rules = [
        'name' => 'required|min:3|unique:groups',
        'shortname' => 'nullable|alpha_num|min:3|unique:groups',
        'description' => 'required|min:3',
        'covenant' => 'required|min:3',
        'city' => 'required',
        'state' => 'required',
        'country' => 'required',
        'type' => 'required',
        'inviter' => 'required',
        'min_donation' => 'required|numeric',
        'donation_frequency' => 'required',
    ];

    public function isValid()
    {
        $rules = static::$rules;
        $rules['name'] = $rules['name'].',name,'.$this->id;
        $rules['shortname'] = $rules['shortname'].',shortname,'.$this->id;
        $validation = Validator::make($this->attributes, $rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public static function validateUserPivot($input)
    {
        static $rules = [
            'role' => 'in:owner,admin,member',
            'is_absent' => 'boolean',
            'email_notification' => 'in:use_default,daily_digest,daily_per_discussion,per_comment,none',
        ];
        $validation = Validator::make($input, $rules);

        return ($validation->passes()) ? null : $validation->messages();
    }

    public static function myGroupsSelectArray()
    {
        return Auth::User()->groups()->get()->pluck('name', 'id')->toArray();
    }

    public function removeMember($user)
    {
        $this->members()->detach($user);
        if ($user->default_group_id == $this->id) {
            $user->resetDefaultGroup(); // this'll reset their default group
        }
    }

    public function getOneSignalIDs()
    {
        $this->load('mobile_notification_members.mobile_apps_notify');
        $one_signal_ids = [];
        foreach ($this->mobile_notification_members as $user) {
            foreach ($user->mobile_apps_notify as $app_acct) {
                $one_signal_ids[] = $app_acct->one_signal_id;
            }
        }

        return $one_signal_ids;
    }
}
