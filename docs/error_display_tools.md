# Error Display Tools - Common Change Application

## 1. JavaScript Error Messages
- **Smooth fade-in animations** for error messages
- Used in forms across the application (landing, verification, registration)
- Implementation:
  ```javascript
  function showError(message) {
      // Clear existing errors
      errorElement.textContent = '';

      // Set new error message
      errorElement.textContent = message || 'An error occurred. Please try again.';

      // Show with fade-in effect
      errorElement.style.opacity = '0';
      errorElement.style.display = 'block';

      setTimeout(() => {
          errorElement.style.transition = 'opacity 0.3s ease';
          errorElement.style.opacity = '1';
      }, 10);
  }
  ```

## 2. Laravel Error Pages
- **HTTP Error Pages** (401, 403, 404, 419, 429, 500, 503)
- Two template styles available:
  - Minimal template (`resources/views/errors/minimal.blade.php`)
  - Illustrated layout (`resources/views/errors/illustrated-layout.blade.php`)
  - Custom layouts (`resources/views/errors/404.blade.php`, `resources/views/errors/500.blade.php`)

## 3. Session Flash Messages
- **Alert system** for displaying messages after redirects
- Implementation in `resources/views/common/alerts.blade.php`
- Supports multiple alert types (info, success, warning, danger)
- Example:
  ```php
  // In controller
  return redirect()->route('dashboard')->with('alerts', ['success' => 'Operation completed successfully']);

  // In view (automatically included)
  @include('common.alerts')
  ```

## 4. CSS Styling
- Consistent error styling in `resources/css/modern.2025.css`
- Example:
  ```css
  .error-message {
      color: #d9534f;
      margin-bottom: 15px;
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      background-color: rgba(217, 83, 79, 0.1);
      width: 100%;
      transition: opacity 0.3s ease;
  }
  ```

## 5. AJAX Response Handling
- Consistent JSON response format for API endpoints
- Structure:
  ```json
  {
    "success": false,
    "message": "Error message here"
  }
  ```
- Client-side handling with appropriate error display